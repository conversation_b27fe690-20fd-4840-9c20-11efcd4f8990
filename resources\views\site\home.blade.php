@extends('site.index')
@section('content')
@php
use App\Models\ProductUnits;
use App\Models\Coins;
use App\Models\Countris;
use App\Models\Wishlist;
use App\Models\Compare;
use App\Models\Products;

    if(empty(session()->get('ChangeCountryy'))) {
    
         $Coin=Coins::where('Draw',1)->first();
    $Country=Countris::where('Coin',$Coin->id)->first();
    session()->put('ChangeCountryy',$Country->id);  
      }

$Ses=Countris::find(session()->get('ChangeCountryy'));


use App\Models\HomeEComDesign;
use App\Models\HomeProductEComDesign;
use App\Models\SupPagesEComDesign;
$sub=SupPagesEComDesign::orderBy('id','desc')->first();
$HomeV=HomeEComDesign::orderBy('id','desc')->first();
$HomeVV=HomeProductEComDesign::orderBy('id','desc')->first();

@endphp


     <title>{{$Comp->Name}}</title>
        <!-- Start Modern Main Slider Area -->
        <div class="home-slides owl-carousel owl-theme modern-hero-slider">
            @foreach($Sliders as $slide)
                @if($slide->Type != 1)
                    <div class="main-slider-item modern-slide-item">
                        <div class="modern-slide-overlay"></div>
                        <div class="d-table">
                            <div class="d-table-cell">
                                <div class="container">
                                    <div class="row align-items-center">
                                        <div class="col-lg-6">
                                            <div class="main-slider-content modern-slide-content">
                                                <h1 class="modern-slide-title">{{app()->getLocale() == 'ar' ?$slide->Arabic_Title :$slide->English_Title}}</h1>
                                                <p class="modern-slide-description">{{app()->getLocale() == 'ar' ?$slide->Arabic_Desc :$slide->English_Desc}}</p>

                                                <div class="slider-btn modern-slide-actions">
                                                    <a href="{{url('ShopSite')}}" class="default-btn modern-cta-btn">
                                                        <i class="bx bx-shopping-bag"></i>
                                                        @if(app()->getLocale() == 'ar') تسوق الآن @else Shop Now @endif
                                                    </a>
                                                    <a href="{{url('AboutSite')}}" class="default-btn secondary modern-secondary-btn">
                                                        <i class="bx bx-info-circle"></i>
                                                        @if(app()->getLocale() == 'ar') اعرف المزيد @else Learn More @endif
                                                    </a>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="col-lg-6">
                                            <div class="main-slider-image modern-slide-image">
                                                <div class="modern-image-container">
                                                    <img src="{{URL::to($slide->Image)}}" alt="{{app()->getLocale() == 'ar' ?$slide->Arabic_Title :$slide->English_Title}}" class="modern-hero-img">
                                                    <div class="modern-image-decoration"></div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                @endif
            @endforeach
        </div>
        <!-- End Modern Main Slider Area -->

        <!-- Start Modern Overview Area -->
        <section class="overview-area modern-overview-section pt-100 pb-70">
            <div class="container">
                <div class="row">
                    <div class="col-lg-6 col-md-6 mb-4">
                        <div class="single-overview modern-overview-card">
                            <div class="row align-items-center">
                                <div class="col-lg-6">
                                    <div class="overview-image modern-overview-image">
                                        <img src="{{URL::to($OneAds->Image)}}" alt="ERP Solutions" class="modern-overview-img">
                                        <div class="modern-image-overlay">
                                            <div class="modern-overlay-icon">
                                                <i class="bx bx-trending-up"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-lg-6">
                                    <div class="overview-content modern-overview-content">
                                        <h3 class="modern-overview-title">@if(app()->getLocale() == 'ar') حلول الأعمال @else Business Solutions @endif</h3>
                                        <p class="modern-overview-desc">@if(app()->getLocale() == 'ar') حلول ERP شاملة لإدارة أعمالك بكفاءة @else Comprehensive ERP Solutions for efficient business management @endif</p>
                                        <div class="overview-btn modern-overview-action">
                                            <a href="{{url('ShopSite')}}" class="default-btn modern-overview-btn">
                                                <i class="bx bx-store"></i>
                                                @if(app()->getLocale() == 'ar') استكشف الحلول @else Explore Solutions @endif
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-6 col-md-6 mb-4">
                        <div class="single-overview modern-overview-card">
                            <div class="row align-items-center">
                                <div class="col-lg-6">
                                    <div class="overview-image modern-overview-image">
                                        <img src="{{URL::to($TwoAds->Image)}}" alt="Tech Innovation" class="modern-overview-img">
                                        <div class="modern-image-overlay">
                                            <div class="modern-overlay-icon">
                                                <i class="bx bx-rocket"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-lg-6">
                                    <div class="overview-content modern-overview-content">
                                        <h3 class="modern-overview-title">@if(app()->getLocale() == 'ar') مركز الابتكار @else Innovation Hub @endif</h3>
                                        <p class="modern-overview-desc">@if(app()->getLocale() == 'ar') تقنيات متطورة لمستقبل أفضل @else Cutting-edge technology for a better future @endif</p>
                                        <div class="overview-btn modern-overview-action">
                                            <a href="{{url('ShopSite')}}" class="default-btn modern-overview-btn">
                                                <i class="bx bx-code-alt"></i>
                                                @if(app()->getLocale() == 'ar') اكتشف المزيد @else Discover More @endif
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        <!-- End Modern Overview Area -->

        <!-- Start Modern Support Area -->
        <section class="support-area modern-features-section">
            <div class="container">
                <div class="modern-section-header text-center mb-5">
                    <h2 class="modern-section-title">@if(app()->getLocale() == 'ar') لماذا تختارنا @else Why Choose Us @endif</h2>
                    <p class="modern-section-subtitle">@if(app()->getLocale() == 'ar') حلول ERP احترافية مصممة خصيصاً لأعمالك @else Professional ERP solutions designed specifically for your business @endif</p>
                </div>

                <div class="support-inner-box modern-features-container">
                    <div class="row">
                        @foreach($Befores as $bef)
                        <div class="col-lg-3 col-md-6 mb-4">
                            <div class="single-support modern-feature-card">
                                <div class="modern-feature-icon">
                                    <div class="modern-icon-wrapper">
                                        {!! $bef->Image !!}
                                    </div>
                                </div>

                                <div class="support-content modern-feature-content">
                                    <h3 class="modern-feature-title">{{app()->getLocale() == 'ar' ?$bef->Arabic_Title :$bef->English_Title}}</h3>
                                    <p class="modern-feature-desc">@if(app()->getLocale() == 'ar') خدمة احترافية عالية الجودة @else Professional high-quality service @endif</p>
                                </div>

                                <div class="modern-feature-hover-effect"></div>
                            </div>
                        </div>
                        @endforeach
                    </div>
                </div>
            </div>
        </section>
        <!-- End Modern Support Area -->

        <!-- Start Modern Arrivals Products Area -->
        <section class="arrivals-products-area modern-products-section pt-100 pb-70">
            <div class="container">
                <div class="section-title modern-section-header text-center">
                    <h2 class="modern-section-title">@if(app()->getLocale() == 'ar') أحدث المنتجات @else New Arrivals @endif</h2>
                    <p class="modern-section-subtitle">@if(app()->getLocale() == 'ar') اكتشف أحدث الحلول التقنية @else Discover our latest technology solutions @endif</p>
                </div>

                <div class="row first owl-carousel owl-theme modern-products-carousel">

                    @foreach($ProductsNew as $pro)
                    <div class="col-lg-12 col-sm-12 item">
                        <div class="single-arrivals-products modern-product-card">
                            <div class="arrivals-products-image modern-product-image">
                                <a href="{{url('ProductDetails/'.$pro->id)}}" class="modern-product-link">
                                    <img src="{{URL::to($pro->Image)}}" alt="{{app()->getLocale() == 'ar' ?$pro->P_Ar_Name :$pro->P_En_Name}}" class="modern-product-img">
                                </a>

                                <div class="modern-product-badge">
                                    {{app()->getLocale() == 'ar' ?$pro->Group()->first()->Name :$pro->Group()->first()->NameEn}}
                                </div>

                                @if($pro->Offer == 1)
                                <div class="modern-offer-badge">
                                    <i class="bx bx-purchase-tag"></i>
                                    @if(app()->getLocale() == 'ar') عرض خاص @else Special Offer @endif
                                </div>
                                @endif

                                <ul class="modern-product-actions">
                                    <li>
                                        <a href="{{url('ProductDetails/'.$pro->id)}}" class="modern-action-btn" data-tooltip="@if(app()->getLocale() == 'ar') عرض التفاصيل @else View Details @endif">
                                            <i class="bx bx-show"></i>
                                        </a>
                                    </li>

                                   @if(!empty(auth()->guard('client')->user()->id))
                                    @php
                                       $Wish=Wishlist::where('Product',$pro->id)
                                            ->where('User',auth()->guard('client')->user()->id)->first();

                                       $Comp=Compare::where('Product',$pro->id)
                                            ->where('User',auth()->guard('client')->user()->id)->first();
                                    @endphp

                                    <li>
                                        @if(empty($Wish))
                                        <a href="{{url('AddWish/'.$pro->id)}}" class="modern-action-btn" data-tooltip="@if(app()->getLocale() == 'ar') أضف للمفضلة @else Add to Wishlist @endif">
                                            <i class="bx bx-heart"></i>
                                        </a>
                                        @else
                                        <span class="modern-action-btn active" data-tooltip="@if(app()->getLocale() == 'ar') في المفضلة @else In Wishlist @endif">
                                            <i class="bx bxs-heart"></i>
                                        </span>
                                        @endif
                                    </li>
                                    <li>
                                       @if(empty($Comp))
                                        <a href="{{url('AddCompare/'.$pro->id)}}" class="modern-action-btn" data-tooltip="@if(app()->getLocale() == 'ar') مقارنة @else Compare @endif">
                                            <i class="bx bx-git-compare"></i>
                                        </a>
                                        @else
                                        <span class="modern-action-btn active" data-tooltip="@if(app()->getLocale() == 'ar') في المقارنة @else In Compare @endif">
                                            <i class="bx bx-git-compare"></i>
                                            <i class="bx bx-check-circle modern-check-icon"></i>
                                        </span>
                                        @endif
                                    </li>
                          @endif
                                </ul>
                            </div>

                            <div class="arrivals-products-content modern-product-content">
                                <h3 class="modern-product-title">
                                    <a href="{{url('ProductDetails/'.$pro->id)}}" class="modern-product-name">
                                        {{app()->getLocale() == 'ar' ?$pro->P_Ar_Name :$pro->P_En_Name}}
                                    </a>
                                </h3>

                                <div class="modern-product-category">
                                    {{app()->getLocale() == 'ar' ?$pro->Group()->first()->Name :$pro->Group()->first()->NameEn}}
                                </div>

                                <ul class="modern-product-rating">
                                    @if(empty($pro->rate))
                                        @for($i = 1; $i <= 5; $i++)
                                            <li><i class='bx bx-star'></i></li>
                                        @endfor
                                    @else
                                        @for($i = 1; $i <= 5; $i++)
                                            @if($i <= $pro->rate)
                                                <li><i class='bx bxs-star'></i></li>
                                            @else
                                                <li><i class='bx bx-star'></i></li>
                                            @endif
                                        @endfor
                                    @endif
                                </ul>

                                <div class="modern-product-price">
                                    @php
                                        $Price = ProductUnits::where('Product',$pro->id)->where('Def',1)->first();
                                    @endphp

                                    @if($pro->Offer == 1)
                                        <span class="modern-price-original">
                                            {{$Ses->Coin()->first()->Symbol}}{{round($Price->Price / $Ses->Coin()->first()->Draw)}}
                                        </span>
                                        <span class="modern-price-offer">
                                            {{$Ses->Coin()->first()->Symbol}}{{round($pro->OfferPrice / $Ses->Coin()->first()->Draw)}}
                                        </span>
                                        <span class="modern-discount-badge">
                                            -{{round((($Price->Price - $pro->OfferPrice) / $Price->Price) * 100)}}%
                                        </span>
                                    @else
                                        <span class="modern-price-current">
                                            {{$Ses->Coin()->first()->Symbol}}{{round($Price->Price / $Ses->Coin()->first()->Draw)}}
                                        </span>
                                    @endif
                                </div>

                                <div class="modern-product-actions-bottom">
                                    <a href="{{url('ProductDetails/'.$pro->id)}}" class="modern-view-details-btn">
                                        <i class="bx bx-show"></i>
                                        @if(app()->getLocale() == 'ar') عرض التفاصيل @else View Details @endif
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    @endforeach
                   
                </div>
            </div>
        </section>
        <!-- End Arrivals Products Area -->

        <!-- Start Offer Products Area -->
        <section class="offer-products-area pb-70">
            <div class="container">
                <div class="section-title modern-section-header text-center">
                    <h2 class="modern-section-title">@if(app()->getLocale() == 'ar') عروض خاصة @else Special Offers @endif</h2>
                    <p class="modern-section-subtitle">@if(app()->getLocale() == 'ar') اكتشف أفضل العروض والخصومات @else Discover the best deals and discounts @endif</p>
                </div>

             
                <div class="row  first owl-carousel owl-theme">
                    
               @foreach($ProductsOffers as $pro)        
              <div class="col-lg-12 col-md-12 item">
                        <div class="single-offer-products">
                            <div class="offer-products-image">
                                <a href="{{url('ProductDetails/'.$pro->id)}}"><img src="{{URL::to($pro->Image)}}" alt="image"></a>
                            </div>
        
                            <div class="offer-products-content">
              <span style="color: {{$HomeVV->Special_Offer_Product_Txt_Color}}"> {{app()->getLocale() == 'ar' ?$pro->Group()->first()->Name :$pro->Group()->first()->NameEn}}    </span>
                                <h3>
           <a href="{{url('ProductDetails/'.$pro->id)}}" style="color: {{$HomeVV->Special_Offer_Product_Txt_Color}}">{{app()->getLocale() == 'ar' ?$pro->P_Ar_Name :$pro->P_En_Name}}</a>
                                </h3>
                                <div class="price">
                                    
                                    
                                         @php  $Price=ProductUnits::where('Product',$pro->id)->where('Def',1)->first();   @endphp  
     @if($pro->Offer == 1)
      
 <span style="color: {{$HomeVV->Special_Offer_Product_Price_Color}}" class="new-price">{{$Ses->Coin()->first()->Symbol}}{{round($pro->OfferPrice / $Ses->Coin()->first()->Draw)}}</span>
    <span style="color: {{$HomeVV->Special_Offer_Product_Price_Color}}" class="old-price">{{$Ses->Coin()->first()->Symbol}}{{round($Price->Price / $Ses->Coin()->first()->Draw)}}</span>            
                                    
        @else

         <span style="color: {{$HomeVV->Special_Offer_Product_Price_Color}}" class="new-price">{{$Ses->Coin()->first()->Symbol}}{{round($Price->Price / $Ses->Coin()->first()->Draw)}}</span>                            
        @endif   
         
                                </div>
                                <ul class="rating">
                                    <li>
                                                  @if(empty($pro->rate))
                                            <i style="color: {{$HomeVV->Special_Offer_Product_Rate_Color}}" class='bx bx-star'></i>
                                        <i style="color: {{$HomeVV->Special_Offer_Product_Rate_Color}}" class='bx bx-star'></i>
                                        <i style="color: {{$HomeVV->Special_Offer_Product_Rate_Color}}" class='bx bx-star'></i>
                                        <i style="color: {{$HomeVV->Special_Offer_Product_Rate_Color}}" class='bx bx-star'></i>
                                        <i style="color: {{$HomeVV->Special_Offer_Product_Rate_Color}}" class='bx bx-star'></i>
                                            @elseif($pro->rate == 1) 
                                     <i style="color: {{$HomeVV->Special_Offer_Product_Rate_Color}}" class='bx bxs-star'></i>
                                        <i style="color: {{$HomeVV->Special_Offer_Product_Rate_Color}}" class='bx bx-star'></i>
                                        <i style="color: {{$HomeVV->Special_Offer_Product_Rate_Color}}" class='bx bx-star'></i>
                                        <i style="color: {{$HomeVV->Special_Offer_Product_Rate_Color}}" class='bx bx-star'></i>
                                        <i style="color: {{$HomeVV->Special_Offer_Product_Rate_Color}}" class='bx bx-star'></i>
                                            @elseif($pro->rate == 2)  
                                 <i style="color: {{$HomeVV->Special_Offer_Product_Rate_Color}}" class='bx bxs-star'></i>
                                        <i style="color: {{$HomeVV->Special_Offer_Product_Rate_Color}}" class='bx bxs-star'></i>
                                        <i style="color: {{$HomeVV->Special_Offer_Product_Rate_Color}}" class='bx bx-star'></i>
                                        <i style="color: {{$HomeVV->Special_Offer_Product_Rate_Color}}" class='bx bx-star'></i>
                                        <i style="color: {{$HomeVV->Special_Offer_Product_Rate_Color}}" class='bx bx-star'></i>
                                            @elseif($pro->rate == 3) 
                                  <i style="color: {{$HomeVV->Special_Offer_Product_Rate_Color}}" class='bx bxs-star'></i>
                                        <i style="color: {{$HomeVV->Special_Offer_Product_Rate_Color}}" class='bx bxs-star'></i>
                                        <i style="color: {{$HomeVV->Special_Offer_Product_Rate_Color}}" class='bx bxs-star'></i>
                                        <i style="color: {{$HomeVV->Special_Offer_Product_Rate_Color}}" class='bx bx-star'></i>
                                        <i style="color: {{$HomeVV->Special_Offer_Product_Rate_Color}}" class='bx bx-star'></i>
                                            @elseif($pro->rate == 4)  
                                  <i style="color: {{$HomeVV->Special_Offer_Product_Rate_Color}}" class='bx bxs-star'></i>
                                        <i style="color: {{$HomeVV->Special_Offer_Product_Rate_Color}}" class='bx bxs-star'></i>
                                        <i style="color: {{$HomeVV->Special_Offer_Product_Rate_Color}}" class='bx bxs-star'></i>
                                        <i style="color: {{$HomeVV->Special_Offer_Product_Rate_Color}}" class='bx bxs-star'></i>
                                        <i style="color: {{$HomeVV->Special_Offer_Product_Rate_Color}}" class='bx bx-star'></i>
                                            @elseif($pro->rate == 5)
                                     <i style="color: {{$HomeVV->Special_Offer_Product_Rate_Color}}" class='bx bxs-star'></i>
                                        <i style="color: {{$HomeVV->Special_Offer_Product_Rate_Color}}" class='bx bxs-star'></i>
                                        <i style="color: {{$HomeVV->Special_Offer_Product_Rate_Color}}" class='bx bxs-star'></i>
                                        <i style="color: {{$HomeVV->Special_Offer_Product_Rate_Color}}" class='bx bxs-star'></i>
                                        <i style="color: {{$HomeVV->Special_Offer_Product_Rate_Color}}" class='bx bxs-star'></i>
                                            @endif  
                                    
                                           </li> 
                                </ul>
                            </div>
                        </div>
                    </div>
                   @endforeach
                </div>
            </div>
        </section>
        <!-- End Offer Products Area -->

        <!-- Start Collection Area -->
        <section class="collection-area">
            <div class="container">
                <div class="collection-inner-box">
                    <div class="row align-items-center">
                        <div class="col-lg-4 col-md-6">
                            <div class="collection-image">
                                <img src="{{URL::to($ThreeAds->Image)}}" alt="image">
                            </div>
                        </div>

                        <div class="col-lg-4 col-md-6">
                            <div class="collection-content">

                                <div class="collection-btn">
                                    <a href="{{url('ShopSite')}}" class="default-btn">
                                        <i class="bx bx-shopping-bag"></i>
                                        @if(app()->getLocale() == 'ar') تسوق الآن @else Shop Now @endif
                                        <span></span>
                                    </a>
                                </div>
                            </div>
                        </div>

                        <div class="col-lg-4 col-md-6 offset-lg-0 offset-md-3">
                            <div class="collection-image">
                                <img src="{{URL::to($FourAds->Image)}}" alt="image">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        <!-- End Collection Area -->

   
        <!-- Start Bestsellers Area -->
        <section class="bestsellers-area pt-100 pb-70">
            <div class="container">
                <div class="section-title" style=" border-bottom:1px solid {{$HomeVV->Best_Sellers_Title_Txt_Color}}" >
                    <h2 style=" background: :{{$HomeVV->Best_Sellers_Title_BG_Color}}; color: {{$HomeVV->Best_Sellers_Title_Txt_Color}}" >Bestsellers</h2>
                </div>
        
                <div class="tab bestsellers-list-tab">
                    <ul class="tabs">
                        
                    @foreach($Groups as $grop)    
                        <li>
                            <a href="#">{{app()->getLocale() == 'ar' ?$grop->Name :$grop->NameEn}}</a>
                        </li>
                    @endforeach    
               
                    </ul>

                    <div class="tab_content">
                               @foreach($Groups as $grop) 
                        @php  
                        
                        $ProductsMost=Products::whereIn('Store_Show',[1,3])
      ->whereIn('P_Type',['Completed','Single_Variable','Duble_Variable'])
            ->where('Store_Type',2)
            ->where('Group',$grop->id)
    ->orderBy('id','desc')->take(50)->get();    
                        
                        @endphp
                        <div class="tabs_item">
                            <div class="row">
                                
                                         @foreach($ProductsMost as $pro)  
                                <div class="col-lg-3 col-sm-6">
                                    <div class="single-bestsellers-products">
                                        <div class="bestsellers-products-image">
                                            <a href="{{url('ProductDetails/'.$pro->id)}}"><img src="{{URL::to($pro->Image)}}" alt="image"></a>
                                            <div class="tag">  {{app()->getLocale() == 'ar' ?$pro->Group()->first()->Name :$pro->Group()->first()->NameEn}}</div>
                                            <ul class="bestsellers-action">
                                                 <li>
                                        <a href="{{url('ProductDetails/'.$pro->id)}}">
                                            <i class="flaticon-shopping-cart"></i>
                                        </a>
                                    </li>
                                    
                                   @if(!empty(auth()->guard('client')->user()->id))            
                                    @php            
                                       $Wish=Wishlist::where('Product',$pro->id)
                                            ->where('User',auth()->guard('client')->user()->id)->first(); 
                                       
                                                       $Comp=Compare::where('Product',$pro->id)
                                            ->where('User',auth()->guard('client')->user()->id)->first(); 
                                                
                                    @endphp
          
                                    <li>
                                        
                                        @if(empty($Wish))     
                                        <a href="{{url('AddWish/'.$pro->id)}}"><i class="flaticon-heart"></i></a>
                                             @else   
                                       
                                        <i style="color:{{$HomeVV->Best_Sellers_Product_Icon_Txt_Color}}" class="bx bxs-heart"></i>
                                            @endif 
                                    </li>
                                    <li>
                                       @if(empty($Comp))      
                                        <a href="{{url('AddCompare/'.$pro->id)}}"><i class="bx bx-git-compare"></i></a>
                                        @else
                                        
                                        <i style="color:{{$HomeVV->Best_Sellers_Product_Icon_Txt_Color}}" class="bx bx-git-compare">  <i style="color:{{$HomeVV->Best_Sellers_Product_Icon_Txt_Color}}" class="bx bx-badge-check"></i></i>
                                      
                                          @endif  
                                    </li>
                          @endif    
                                            </ul>
                                        </div>
            
                                        <div class="bestsellers-products-content">
                                            <h3>
      <a href="{{url('ProductDetails/'.$pro->id)}}"  style="color:{{$HomeVV->Best_Sellers_Product_Txt_Color}}">   {{app()->getLocale() == 'ar' ?$pro->P_Ar_Name :$pro->P_En_Name}}     </a>
                                            </h3>
                                            <ul class="rating">
                                             @if(empty($pro->rate))
                                     <li><i style="color:{{$HomeVV->Best_Sellers_Product_Rate_Color}}" class='bx bx-star'></i></li>
                                    <li><i  style="color:{{$HomeVV->Best_Sellers_Product_Rate_Color}}" class='bx bx-star'></i></li>
                                    <li><i  style="color:{{$HomeVV->Best_Sellers_Product_Rate_Color}}" class='bx bx-star'></i></li>
                                    <li><i  style="color:{{$HomeVV->Best_Sellers_Product_Rate_Color}}" class='bx bx-star'></i></li>
                                    <li><i  style="color:{{$HomeVV->Best_Sellers_Product_Rate_Color}}" class='bx bx-star'></i></li>
                                            @elseif($pro->rate == 1) 
                                     <li><i  style="color:{{$HomeVV->Best_Sellers_Product_Rate_Color}}" class='bx bxs-star'></i></li>
                                    <li><i  style="color:{{$HomeVV->Best_Sellers_Product_Rate_Color}}" class='bx bx-star'></i></li>
                                    <li><i  style="color:{{$HomeVV->Best_Sellers_Product_Rate_Color}}" class='bx bx-star'></i></li>
                                    <li><i  style="color:{{$HomeVV->Best_Sellers_Product_Rate_Color}}" class='bx bx-star'></i></li>
                                    <li><i  style="color:{{$HomeVV->Best_Sellers_Product_Rate_Color}}" class='bx bx-star'></i></li>
                                            @elseif($pro->rate == 2)  
                                     <li><i  style="color:{{$HomeVV->Best_Sellers_Product_Rate_Color}}" class='bx bxs-star'></i></li>
                                    <li><i  style="color:{{$HomeVV->Best_Sellers_Product_Rate_Color}}" class='bx bxs-star'></i></li>
                                    <li><i  style="color:{{$HomeVV->Best_Sellers_Product_Rate_Color}}" class='bx bx-star'></i></li>
                                    <li><i  style="color:{{$HomeVV->Best_Sellers_Product_Rate_Color}}" class='bx bx-star'></i></li>
                                    <li><i  style="color:{{$HomeVV->Best_Sellers_Product_Rate_Color}}" class='bx bx-star'></i></li>
                                            @elseif($pro->rate == 3) 
                                     <li><i  style="color:{{$HomeVV->Best_Sellers_Product_Rate_Color}}" class='bx bxs-star'></i></li>
                                    <li><i  style="color:{{$HomeVV->Best_Sellers_Product_Rate_Color}}" class='bx bxs-star'></i></li>
                                    <li><i  style="color:{{$HomeVV->Best_Sellers_Product_Rate_Color}}" class='bx bxs-star'></i></li>
                                    <li><i  style="color:{{$HomeVV->Best_Sellers_Product_Rate_Color}}" class='bx bx-star'></i></li>
                                    <li><i  style="color:{{$HomeVV->Best_Sellers_Product_Rate_Color}}" class='bx bx-star'></i></li>
                                            @elseif($pro->rate == 4)  
                                     <li><i  style="color:{{$HomeVV->Best_Sellers_Product_Rate_Color}}" class='bx bxs-star'></i></li>
                                    <li><i  style="color:{{$HomeVV->Best_Sellers_Product_Rate_Color}}" class='bx bxs-star'></i></li>
                                    <li><i  style="color:{{$HomeVV->Best_Sellers_Product_Rate_Color}}" class='bx bxs-star'></i></li>
                                    <li><i  style="color:{{$HomeVV->Best_Sellers_Product_Rate_Color}}" class='bx bxs-star'></i></li>
                                    <li><i  style="color:{{$HomeVV->Best_Sellers_Product_Rate_Color}}" class='bx bx-star'></i></li>
                                            @elseif($pro->rate == 5)
                                     <li><i  style="color:{{$HomeVV->Best_Sellers_Product_Rate_Color}}" class='bx bxs-star'></i></li>
                                    <li><i  style="color:{{$HomeVV->Best_Sellers_Product_Rate_Color}}" class='bx bxs-star'></i></li>
                                    <li><i  style="color:{{$HomeVV->Best_Sellers_Product_Rate_Color}}" class='bx bxs-star'></i></li>
                                    <li><i  style="color:{{$HomeVV->Best_Sellers_Product_Rate_Color}}" class='bx bxs-star'></i></li>
                                    <li><i  style="color:{{$HomeVV->Best_Sellers_Product_Rate_Color}}" class='bx bxs-star'></i></li>
                                            @endif  
                                            </ul>
                                                                     <span style="color:{{$HomeVV->Best_Sellers_Product_Price_Color}}">
                                
     @php  $Price=ProductUnits::where('Product',$pro->id)->where('Def',1)->first();   @endphp  
     @if($pro->Offer == 1)
<del style="color:{{$HomeVV->Best_Sellers_Product_Price_Color}}">{{$Ses->Coin()->first()->Symbol}}{{round($Price->Price / $Ses->Coin()->first()->Draw)}}</del>
{{$Ses->Coin()->first()->Symbol}}{{round($pro->OfferPrice / $Ses->Coin()->first()->Draw)}}
        @else
{{$Ses->Coin()->first()->Symbol}}{{round($Price->Price / $Ses->Coin()->first()->Draw)}}
        @endif     
                                
                                
                                </span>
                                        </div>
                                    </div>
                                </div>
                                @endforeach
                                
                              
                                
                              

                            </div>
                        </div>
                            @endforeach
                     
                    </div>
                </div>
            </div>
        </section>
        <!-- End Bestsellers Area -->


        <!-- Start Blog Area -->
        <section class="blog-area pb-70">
            <div class="container">
                <div class="section-title modern-section-header text-center">
                    <h2 class="modern-section-title">@if(app()->getLocale() == 'ar') أحدث المقالات @else Latest Blog @endif</h2>
                    <p class="modern-section-subtitle">@if(app()->getLocale() == 'ar') اطلع على آخر الأخبار والمقالات التقنية @else Stay updated with our latest news and tech articles @endif</p>
                </div>

                <div class="row owl-carousel new-carousel">
                    
                         @foreach($Articles as $art)
                    <div class="col-lg-12 item col-md-6 ">
                        <div class="single-blog">
                            <div class="blog-image">
                                <a href="{{url('BlogDetails/'.$art->id)}}">
                                    <img src="{{URL::to($art->Sub_Image)}}" alt="image">
                                </a>
                            </div>



                            <div class="blog-content">
                            
                                <h3>
                <a style="color: {{$sub->Blogs_Title_Color}}" href="{{url('BlogDetails/'.$art->id)}}">       {{app()->getLocale() == 'ar' ?$art->Arabic_Title :$art->English_Title}}</a>
                                </h3>
                                <div class="post-meta">
                                    <a style="color: {{$sub->Blogs_Txt_Color}}" href="{{url('BlogDetails/'.$art->id)}}">{{$art->Date}}</a> 
                                </div>
                                <p style="display: none">       {!!app()->getLocale() == 'ar' ?$art->Arabic_Desc :$art->English_Desc!!}</p>
                            </div>
                        </div>
                    </div>
                    @endforeach

                </div>
            </div>
        </section>
        <!-- End Blog Area -->

        <!-- Start Partner Area -->
        <div class="partner-area ptb-100" style="background: {{$HomeV->Partners_BG_Color}}">
            <div class="container">
                <div class="partner-slider owl-carousel owl-theme">
                    
                   @foreach($Brands as $brand)
   
                    <div class="partner-item">
                        <a href="{{url('ShopFilterBrand/'.$brand->id)}}">
                            <img src="{{URL::to($brand->Image)}} " alt="image">
                        </a>
                    </div>
                    @endforeach
                   
                </div>
            </div>
        </div>
        <!-- End Partner Area -->

       
            <!-- Slider -->
            <style>
    .slider-btn .default-btn{
        
        background: {{$HomeV->Slider_Button_BG_Color}}  !important;
        color: {{$HomeV->Slider_Button_Txt_Color}}  !important;
    }
    
    
      .slider-btn .default-btn:hover{
        
        background: {{$HomeV->Slider_Button_Hover_BG_Color}}  !important;
        color: {{$HomeV->Slider_Button_Hover_Txt_Color}}  !important;
    }
    
    .slider-btn .default-btn:hover i {

        color: {{$HomeV->Slider_Button_Hover_Txt_Color}}  !important;
}
    
    
    .slider-btn .default-btn:hover span {
        
             background: {{$HomeV->Slider_Button_Hover_BG_Color}}  !important;
        color: {{$HomeV->Slider_Button_Hover_Txt_Color}}  !important;
        
    }
</style>  
            
            <!-- Ads Top -->
            <style>
            .single-overview {
    background-color: {{$HomeV->Ads_Top_Img_First_BG_Color}} !important;
                }
                
                .single-overview::before {
                    
              background-color: {{$HomeV->Ads_Top_Img_First_Before_BG_Color}} !important;         
                }
                
                
                       .w {
    background-color: {{$HomeV->Ads_Top_Img_Second_BG_Color}} !important;
                }
                
                .w::before {
                    
              background-color: {{$HomeV->Ads_Top_Img_Second_Before_BG_Color}} !important;         
                }
                
                
                
                  .single-overview .default-btn{
        
        background: {{$HomeV->Ads_Top_Img_Button_BG_Color}}  !important;
        color: {{$HomeV->Ads_Top_Img_Button_Txt_Color}}  !important;
    }
    
    
      .single-overview .default-btn:hover{
        
        background: {{$HomeV->Ads_Top_Img_Button_Hover_BG_Color}}  !important;
        color: {{$HomeV->Ads_Top_Img_Button_Hover_Txt_Color}}  !important;
    }
    
    .single-overview .default-btn:hover i {

        color: {{$HomeV->Ads_Top_Img_Button_Hover_Txt_Color}}  !important;
}
    
    
    .single-overview .default-btn:hover span {
        
             background: {{$HomeV->Ads_Top_Img_Button_Hover_BG_Color}}  !important;
        color: {{$HomeV->Ads_Top_Img_Button_Hover_Txt_Color}}  !important;
        
    }
                
            </style>
            
              <!-- Support Icons -->
            <style>
                .single-support .icon i {
                    
                    color: {{$HomeV->Support_Icons_Color}} !important;
                    
                }
            </style>
            
                 <!-- Ads Bottom -->
            <style>
            
                .collection-inner-box{
                   
                     background-color: {{$HomeV->Ads_Bootom_Imgs_BG_Color}} !important;
                    
                }
                
                .collection-inner-box::before {
                    
              background-color: {{$HomeV->Ads_Bootom_Imgs_Middle_BG_Color}} !important;         
                    
                }
                
                
                   .collection-inner-box .default-btn{
        
        background: {{$HomeV->Ads_Bootom_Imgs_Button_BG_Color}}  !important;
        color: {{$HomeV->Ads_Bootom_Imgs_Button_Txt_Color}}  !important;
    }
    
    
      .collection-inner-box .default-btn:hover{
        
        background: {{$HomeV->Ads_Bootom_Imgs_Button_Hover_BG_Color}}  !important;
        color: {{$HomeV->Ads_Bootom_Imgs_Button_Hover_Txt_Color}}  !important;
    }
    
    .collection-inner-box .default-btn:hover i {

        color: {{$HomeV->Ads_Bootom_Imgs_Button_Hover_Txt_Color}}  !important;
}
    
    
    .collection-inner-box .default-btn:hover span {
        
             background: {{$HomeV->Ads_Bootom_Imgs_Button_Hover_BG_Color}}  !important;
        color: {{$HomeV->Ads_Bootom_Imgs_Button_Hover_Txt_Color}}  !important;
        
    }
            </style> 

               <!-- Blogs -->
            <style>
                .single-blog .blog-content h3 a:hover {
                    
                    color: {{$sub->Blogs_Hover_Txt_Color}} !important;   
                }
                
                   .single-blog .blog-content .post-meta a:hover {
                    
                    color: {{$sub->Blogs_Hover_Txt_Color}} !important;   
                }
            </style>
            
            <!-- Special Offer -->
            <style>
                    single-offer-products {
                        border: 1px solid {{$HomeVV->Special_Offer_Product_Border_Color}} !important;
                        background-color: {{$HomeVV->Special_Offer_Product_BG_Color}} !important;
                    }
                     
                     .single-offer-products .offer-products-content h3 a:hover {
                         
                         color: {{$HomeVV->Special_Offer_Product_Txt_Hover_Color}} !important;
                     }
                </style>  
            
            <!-- Best Sellers -->
              <style>
                .bestsellers-list-tab .tabs li a{
                    
                    color: {{$HomeVV->Best_Sellers_Category_Txt_Color}} !important;
                }  
                
                .bestsellers-list-tab .tabs li.current a {
      color: {{$HomeVV->Best_Sellers_Category_Active_Txt_Color}} !important;
    border-bottom: 1px solid {{$HomeVV->Best_Sellers_Category_Active_Txt_Color}} !important;
}
                
                
        .single-bestsellers-products .bestsellers-products-image .tag {

    background: {{$HomeVV->Best_Sellers_Product_Group_BG_Color}} !important;
    color: {{$HomeVV->Best_Sellers_Product_Group_Txt_Color}} !important;
            
                }
                
                      .single-bestsellers-products .bestsellers-products-image .tag:hover {

    background: {{$HomeVV->Best_Sellers_Product_Group_Hover_BG_Color}} !important;
    color: {{$HomeVV->Best_Sellers_Product_Group_Hover_Txt_Color}} !important;
            
                }
                
                .single-bestsellers-products {
                    
             background: {{$HomeVV->Best_Sellers_Product_BG_Color}} !important;         
                }
                
                
                .single-bestsellers-products .bestsellers-products-image .bestsellers-action li a i {

                    background-color:{{$HomeVV->Best_Sellers_Product_Icon_BG_Color}} !important;
                    color: {{$HomeVV->Best_Sellers_Product_Icon_Txt_Color}} !important;
                
                }
                
                            .single-bestsellers-products .bestsellers-products-image .bestsellers-action li a i:hover {

                                background-color:{{$HomeVV->Best_Sellers_Product_Icon_Hover_BG_Color}} !important;
                                color: {{$HomeVV->Best_Sellers_Product_Icon_Hover_Txt_Color}} !important;
                
                }
            </style>
            
                <!-- New Arrivals -->
            <style>
                .single-arrivals-products .arrivals-products-image .tag{
                
                        background: {{$HomeVV->New_Arrivals_Product_Group_BG_Color}} !important;
    color: {{$HomeVV->New_Arrivals_Product_Group_Txt_Color}} !important;
                    
                }
                
                .single-arrivals-products .arrivals-products-image .tag:hover{
                  
                                background: {{$HomeVV->New_Arrivals_Product_Group_Hover_BG_Color}} !important;
    color: {{$HomeVV->New_Arrivals_Product_Group_Hover_Txt_Color}} !important;
                    
                }
                
                
                .single-arrivals-products .arrivals-products-image .arrivals-action li a i{
                    
                         background-color:{{$HomeVV->New_Arrivals_Product_Icon_BG_Color}} !important;
                    color: {{$HomeVV->New_Arrivals_Product_Icon_Txt_Color}} !important;
                    
                }        
                
                
                .single-arrivals-products .arrivals-products-image .arrivals-action li a i:hover{
                    
                         background-color:{{$HomeVV->New_Arrivals_Product_Icon_Hover_BG_Color}} !important;
                    color: {{$HomeVV->New_Arrivals_Product_Icon_Hover_Txt_Color}} !important;
                    
                }              
                
                .single-arrivals-products .arrivals-products-content span:hover {

                   color: {{$HomeVV->New_Arrivals_Product_Hover_Price_Color}} !important;   
                }



            
            </style>

@endsection    