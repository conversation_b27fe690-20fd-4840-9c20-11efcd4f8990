<?php $__env->startSection('content'); ?>
<?php
use App\Models\ProductUnits;
use App\Models\Countris;
use App\Models\Rate;
use App\Models\Wishlist;
use App\Models\Compare;
use App\Models\ItemsGroups;
use App\Models\Coins;
    if(empty(session()->get('ChangeCountryy'))) {
    $Coin=Coins::where('Draw',1)->first();
    $Country=Countris::where('Coin',$Coin->id)->first();
    session()->put('ChangeCountryy',$Country->id);  
      }
$Ses=Countris::find(session()->get('ChangeCountryy'));
use App\Models\MainEComDesign;
use App\Models\SupPagesPartTwoEComDesign;
$main=MainEComDesign::orderBy('id','desc')->first();
$sub=SupPagesPartTwoEComDesign::orderBy('id','desc')->first();
?>

 <style>
.page-title-area {
    background-color: <?php echo e($main->Breadcumb_BG_Color); ?> !important;
}
     
    .cart-area {
      background-color: <?php echo e($main->Sub_Page_BG_Color); ?> !important;
} 
     
 
    
     .cart-buttons .default-btn{
        
        background: <?php echo e($sub->Cart_Button_BG_Color); ?>  !important;
        color: <?php echo e($sub->Cart_Button_Txt_Color); ?>  !important;
    }
    
    
     .cart-buttons .default-btn:hover{
        
        background: <?php echo e($sub->Cart_Button_BG_Hover_Color); ?>  !important;
        color: <?php echo e($sub->Cart_Button_Txt_Hover_Color); ?>  !important;
    }
    
   .cart-buttons .default-btn:hover i {

        color: <?php echo e($sub->Cart_Button_Txt_Hover_Color); ?>  !important;
}
    
    
   .cart-buttons .default-btn:hover span {
        
             background: <?php echo e($sub->Cart_Button_BG_Hover_Color); ?>  !important;
        color: <?php echo e($sub->Cart_Button_Txt_Hover_Color); ?>  !important;
        
    }
     
     
     
    .cart-totals .default-btn{
        
        background: <?php echo e($sub->Cart_Box_Button_BG_Color); ?>  !important;
        color: <?php echo e($sub->Cart_Box_Button_Txt_Color); ?>  !important;
    }
    
    
     .cart-totals .default-btn:hover{
        
        background: <?php echo e($sub->Cart_Box_Button_BG_Hover_Color); ?>  !important;
        color: <?php echo e($sub->Cart_Box_Button_Txt_Hover_Color); ?>  !important;
    }
    
   .cart-totals .default-btn:hover i {

        color: <?php echo e($sub->Cart_Box_Button_Txt_Hover_Color); ?>  !important;
}
    
    
   .cart-totals .default-btn:hover span {
        
             background: <?php echo e($sub->Cart_Box_Button_BG_Hover_Color); ?>  !important;
        color: <?php echo e($sub->Cart_Box_Button_Txt_Hover_Color); ?>  !important;
        
    }
     
     
     .cart-buttons .shopping-coupon-code button{
           background: <?php echo e($sub->Cart_Cupon_Button_BG_Color); ?>  !important;
        color: <?php echo e($sub->Cart_Cupon_Button_Txt_Color); ?>  !important;
     }
     
        .cart-buttons .shopping-coupon-code button:hover{
           background: <?php echo e($sub->Cart_Cupon_Button_BG_Hover_Color); ?>  !important;
        color: <?php echo e($sub->Cart_Cupon_Button_Txt_Hover_Color); ?>  !important;
     }
     
     .cart-buttons .shopping-coupon-code .form-control{
             background: <?php echo e($sub->Cart_Cupon_Input_BG_Color); ?>  !important;
     }
     .cart-totals {
                     background: <?php echo e($sub->Cart_Box_BG_Color); ?>  !important; 
         
     }
     
     .cart-totals ul li {
         
            color: <?php echo e($sub->Cart_Box_Title_Color); ?>  !important;
     }
     
     
     .cart-totals ul li span{
            color: <?php echo e($sub->Cart_Box_Txt_Color); ?>  !important;
         
     }
     
       .cart-table table thead {
    background-color: <?php echo e($main->Table_Header_BG_Color); ?> !important;
    color: <?php echo e($main->Table_Header_Txt_Color); ?> !important;
}
     
     
     .table>tbody {
           background-color: <?php echo e($main->Table_Body_BG_Color); ?> !important;
   
         
     }
     
     .table-striped>tbody>tr:nth-of-type(odd)>* {
       color: <?php echo e($main->Table_Body_Txt_Color); ?> !important;     
     }
     
     .cart-table table tbody tr td {
              color: <?php echo e($main->Table_Body_Txt_Color); ?> !important;      
         
     }
     
</style>
   <title><?php echo e(trans('admin.Cart')); ?></title>

        <!-- Start Page Banner -->
        <div class="page-title-area">
            <div class="container">
                <div class="page-title-content">
                    <h2 style="color: <?php echo e($main->Breadcumb_Txt_Color); ?> !important"><?php echo e(trans('admin.Cart')); ?></h2>

                    <ul>
                        <li><a style="color: <?php echo e($main->Breadcumb_Txt_Color); ?> !important" href="<?php echo e(url('/')); ?>"><?php echo e(trans('admin.Home')); ?></a></li>
                        <li style="color: <?php echo e($main->Breadcumb_Txt_Color); ?> !important"><?php echo e(trans('admin.Cart')); ?></li>
                    </ul>
                </div>
            </div>
        </div>
        <!-- End Page Banner -->

<?php if(Cart::content()->count() == 0): ?>
<section class="cart-area ptb-50">
    <div class="container">
    <div class="row">
    
        
        <span class="alert alert-danger text-center"><?php echo e(trans('admin.Cart_is_Empty')); ?></span>
        
                  <div class="overview-btn">
                                            <a href="<?php echo e(url('ShopSite')); ?>" class="default-btn">
                                                <i class="flaticon-shopping-cart"></i>
                                             <?php echo e(trans('admin.Shop_Now')); ?>

                                                <span></span>
                                            </a>
                                        </div>
        
    </div>
    </div>
</section>

<?php else: ?>
        <!-- Start Cart Area -->
		<section class="cart-area ptb-50">
            <div class="container">
                <div class="row">
                    <div class="col-lg-8 col-md-12">
                        <form action="<?php echo e(url('UpdateCart')); ?>" method="post">
                            <?php echo csrf_field(); ?>
                  
                            <div class="cart-table table-responsive">
                                <table class="table table-bordered">
                                    <thead>
                                        <tr>
                                            <th scope="col"></th>
                                            <th scope="col"><?php echo e(trans('admin.Product')); ?></th>
                                            <th scope="col"><?php echo e(trans('admin.Price')); ?></th>
                                            <th scope="col"><?php echo e(trans('admin.Qty')); ?></th>
                                            <th scope="col"><?php echo e(trans('admin.Total')); ?></th>
                                        </tr>
                                    </thead>
        
                                    <tbody>
                                        
                                               <?php $__currentLoopData = $Carts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $cart): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>  
                                           <input type="hidden" name="RowID[]" value="<?php echo e($cart->rowId); ?>">     
                                      <input type="hidden" name="AvQty[]" value="<?php echo e($cart->options->AvQty); ?>">     



                                        <tr class="top-class">
                                            <td class="product-thumbnail">
                                                
                                         

                                                <a href="<?php echo e(url('DeleteCart/'.$cart->rowId)); ?>" class="remove"><i class='bx bx-x'></i></a>

                                                <a href="<?php echo e(url('ProductDetails/'.$cart->id)); ?>">
                                                    <img src="<?php echo e(URL::to($cart->options->image)); ?>" alt="item">
                                                </a>
                                            </td>
        
                                            <td class="product-name">
                                                <a href="<?php echo e(url('ProductDetails/'.$cart->id)); ?>">
                                                
                                                    <?php echo e(app()->getLocale() == 'ar' ?$cart->name :$cart->options->other_name); ?>

                                                    
                                                
                                                </a>
                                            </td>
        
                                            <td class="product-price">
                                                <span class="unit-amount"><?php echo e($cart->price); ?> <?php echo e($Ses->Coin()->first()->Symbol); ?></span>
                                            </td>
        
                                            <td class="product-quantity">
                                                <div class="input-counter">
                                                    <span class="minus-btn"><i class='bx bx-minus'></i></span>
                                                    <input type="text" name="qty[]" id="qty" value="<?php echo e($cart->qty); ?>" value="1" readonly>
                                                    <span class="plus-btn"><i class='bx bx-plus'></i></span>
                                                </div>
                                            </td>
        
                                            <td class="product-subtotal">
                                                <span class="subtotal-amount">
                                                
                                            <?php echo e($cart->total); ?> <?php echo e($Ses->Coin()->first()->Symbol); ?>    
                                                </span>
                                            </td>
                                        </tr>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                      
                                    </tbody>
                                </table>
                            </div>
        
                            <div class="cart-buttons">
                                <div class="row align-items-center">
                            
        
                                    <div class="col-lg-12 col-sm-12 col-md-12">
                                        <button type="submit" class="default-btn" style="width: 100%">
                                       <?php echo e(trans('admin.Update_Cart')); ?>

                                            <span></span>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>

                    <div class="col-lg-4 col-md-12">
                             <div class="cart-buttons">
                               <form action="<?php echo e(url('UpdateCuponCode')); ?>" method="post">
                        <?php echo csrf_field(); ?>         
                        <div class="row align-items-center">
                                        <div class="shopping-coupon-code">
                                      
                 <?php if($x == 0): ?>  
              <input type="text" class="form-control" placeholder="<?php echo e(trans('admin.Discount_Code')); ?>" name="code" id="code">
                        <?php else: ?>
                         <input type="text" class="form-control" placeholder="<?php echo e(trans('admin.Discount_Code')); ?>" name="code" id="code" readonly>           
                        <?php endif; ?>  
                                            
                           
                                            <button type="submit"> <?php echo e(trans('admin.Apply_Coupon')); ?></button>
                                        </div>
                                    </div>    
                                 </form>
                                    </div>        
                        <div class="cart-totals">
                            <h3>
                        <?php echo e(trans('admin.Cart_Totals')); ?>    
                            </h3>
    
                            <ul>
                                <li><?php echo e(trans('admin.Sub_Total')); ?> <span>   <?php echo e(Cart::subtotal()); ?><?php echo e($Ses->Coin()->first()->Symbol); ?></span></li>
                                <li><?php echo e(trans('admin.Cupon_Code')); ?> <span><?php echo e($x); ?>  <?php echo e($Ses->Coin()->first()->Symbol); ?></span></li>
                                <li><?php echo e(trans('admin.Grand_Total')); ?> <span> <?php echo e(str_replace(',', '', Cart::total()) - $x); ?> <?php echo e($Ses->Coin()->first()->Symbol); ?></span></li>
                               <?php    session()->put('CODE',$x); ?>
                            </ul>
    
                            <a href="<?php echo e(url('Checkout')); ?>" class="default-btn">
                                  <?php echo e(trans('admin.Checkout')); ?>

                                <span></span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        <!-- End Cart Area -->
<?php endif; ?>

<?php $__env->stopSection(); ?>
<?php echo $__env->make('site.index', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\ost-erp-online\resources\views/site/Cart.blade.php ENDPATH**/ ?>