<?php $__env->startSection('content'); ?>
<?php
use App\Models\ProductUnits;
use App\Models\Countris;
use App\Models\Rate;
use App\Models\Wishlist;
use App\Models\Compare;
use App\Models\ItemsGroups;
use App\Models\Coins;
    if(empty(session()->get('ChangeCountryy'))) {
    $Coin=Coins::where('Draw',1)->first();
    $Country=Countris::where('Coin',$Coin->id)->first();
    session()->put('ChangeCountryy',$Country->id);  
      }
$Ses=Countris::find(session()->get('ChangeCountryy'));
use App\Models\MainEComDesign;
use App\Models\ProductDetailsEComDesign;
$main=MainEComDesign::orderBy('id','desc')->first();
$suben=ProductDetailsEComDesign::orderBy('id','desc')->first();
?>



 <style>
.page-title-area {
    background-color: <?php echo e($main->Breadcumb_BG_Color); ?> !important;
}
     
    .products-details-area {
      background-color: <?php echo e($main->Sub_Page_BG_Color); ?> !important;
} 
     
     
     .products-details-desc .product-content .price {
   
    color: <?php echo e($suben->Price_Color); ?> !important;
     }
     
     .products-details-desc .product-content .price .old-price{
          color: <?php echo e($suben->Price_Color); ?> !important;
             opacity: 0.5;

     }
     
     .products-details-desc .product-content .product-review .rating i {
    color: <?php echo e($suben->Rate_Color); ?> !important;
}
     
     .rate > input:checked ~ label {
    color:<?php echo e($suben->Rate_Color); ?> !important;
}
     
     .products-details-tabs .nav .nav-item .nav-link {

    border-bottom: 1px solid <?php echo e($suben->Title_Color); ?> !important;
    color: <?php echo e($suben->Title_Color); ?> !important;
     }
     
     .products-details-tabs .nav .nav-item .nav-link:hover, .products-details-tabs .nav .nav-item .nav-link.active{
         
           color: <?php echo e($suben->Title_Color); ?> !important;
     }
     
     .products-details-tabs .tab-content .tab-pane .products-reviews h3{
            color: <?php echo e($suben->Title_Color); ?> !important;
         
     }
     
     .products-details-tabs .tab-content .tab-pane h2{
          color: <?php echo e($suben->Title_Color); ?> !important;
     }
     
     

     
        .products-details-desc .product-content .product-add-to-cart .default-btn{
        
        background: <?php echo e($suben->Comment_Button_BG_Color); ?>  !important;
        color: <?php echo e($suben->Comment_Button_Txt_Color); ?>  !important;
    }
    
    
      .products-details-desc .product-content .product-add-to-cart .default-btn:hover{
        
        background: <?php echo e($suben->Comment_Button_BG_Hover_Color); ?>  !important;
        color: <?php echo e($suben->Comment_Button_Txt_Hover_Color); ?>  !important;
    }
    
   .products-details-desc .product-content .product-add-to-cart .default-btn:hover i {

        color: <?php echo e($suben->Comment_Button_Txt_Hover_Color); ?>  !important;
}
    
    
  .products-details-desc .product-content .product-add-to-cart .default-btn:hover span {
        
             background: <?php echo e($suben->Comment_Button_BG_Hover_Color); ?>  !important;
        color: <?php echo e($suben->Comment_Button_Txt_Hover_Color); ?>  !important;
        
    }
     
     
     
           .products-details-tabs .tab-content .tab-pane .products-review-form .review-form .default-btn{
        
        background: <?php echo e($suben->Comment_Button_BG_Color); ?>  !important;
        color: <?php echo e($suben->Comment_Button_Txt_Color); ?>  !important;
    }
    
    
     .products-details-tabs .tab-content .tab-pane .products-review-form .review-form .default-btn:hover{
        
        background: <?php echo e($suben->Comment_Button_BG_Hover_Color); ?>  !important;
        color: <?php echo e($suben->Comment_Button_Txt_Hover_Color); ?>  !important;
    }
    
  .products-details-tabs .tab-content .tab-pane .products-review-form .review-form .default-btn:hover i {

        color: <?php echo e($suben->Comment_Button_Txt_Hover_Color); ?>  !important;
}
    
    
 .products-details-tabs .tab-content .tab-pane .products-review-form .review-form .default-btn:hover span {
        
             background: <?php echo e($suben->Comment_Button_BG_Hover_Color); ?>  !important;
        color: <?php echo e($suben->Comment_Button_Txt_Hover_Color); ?>  !important;
        
    }
     
     .products-details-tabs .tab-content .tab-pane .products-review-form .review-form textarea.form-control {

     background: <?php echo e($suben->Comment_Input_BG_Color); ?>  !important;
     }
     
     .section-title {

    border-bottom: 1px solid <?php echo e($suben->Related_Title_Txt_Color); ?>  !important;
     }
     .section-title h2 {
    background: <?php echo e($suben->Related_Title_BG_Color); ?>  !important;
    color: <?php echo e($suben->Related_Title_Txt_Color); ?>  !important;
     }
     
     .single-arrivals-products{
             background: <?php echo e($suben->Related_Product_BG_Color); ?>  !important;
     }
     
     .single-arrivals-products .arrivals-products-image .tag {
  
    background: <?php echo e($suben->Related_Product_Group_BG_Color); ?>  !important;
    color: <?php echo e($suben->Related_Product_Group_Txt_Color); ?>  !important;
     }
     
          .single-arrivals-products .arrivals-products-image .tag:hover {
  
    background: <?php echo e($suben->Related_Product_Group_Hover_BG_Color); ?>  !important;
    color: <?php echo e($suben->Related_Product_Group_Hover_Txt_Color); ?>  !important;
     }
     
     .single-arrivals-products .arrivals-products-image .arrivals-action li a i{
         
           background: <?php echo e($suben->Related_Product_Icon_BG_Color); ?>  !important;
    color: <?php echo e($suben->Related_Product_Icon_Txt_Color); ?>  !important;
     }
     
     
        .single-arrivals-products .arrivals-products-image .arrivals-action li  i{
         
    color: <?php echo e($suben->Related_Product_Icon_Txt_Color); ?>  !important;
     }
     
          .single-arrivals-products .arrivals-products-image .arrivals-action li a i:hover{
         
           background: <?php echo e($suben->Related_Product_Icon_Hover_BG_Color); ?>  !important;
    color: <?php echo e($suben->Related_Product_Icon_Hover_Txt_Color); ?>  !important;
     }
     
     .single-arrivals-products .arrivals-products-content h3 a{
         
          color: <?php echo e($suben->Related_Product_Txt_Color); ?>  !important; 
     }
     
     
     .single-arrivals-products .arrivals-products-content .rating li i {
    color: <?php echo e($suben->Related_Product_Rate_Color); ?>  !important; 
}
     
     .single-arrivals-products .arrivals-products-content span{
           color: <?php echo e($suben->Related_Product_Price_Color); ?>  !important; 
         
     }
     
     
         .single-arrivals-products .arrivals-products-content span del{
           color: <?php echo e($suben->Related_Product_Price_Color); ?>  !important; 
         
     }
     
     
          .single-arrivals-products .arrivals-products-content span:hover{
           color: <?php echo e($suben->Related_Product_Hover_Price_Color); ?>  !important; 
         
     }

</style>
    <title><?php echo e(trans('admin.Product_Details')); ?></title>

              <!-- Start Page Banner -->
        <div class="page-title-area">
            <div class="container">
                <div class="page-title-content">
                    <h2 style="color: <?php echo e($main->Breadcumb_Txt_Color); ?> !important"><?php echo e(trans('admin.Product_Details')); ?></h2>

                    <ul>
                        <li><a style="color: <?php echo e($main->Breadcumb_Txt_Color); ?> !important" href="<?php echo e(url('/')); ?>"><?php echo e(trans('admin.Home')); ?></a></li>
                        <li style="color: <?php echo e($main->Breadcumb_Txt_Color); ?> !important"><?php echo e(trans('admin.Product_Details')); ?></li>
                    </ul>
                </div>
            </div>
        </div>
        <!-- End Page Banner -->

        <!-- Start Products Details Area -->
        <section class="products-details-area ptb-50">
            <div class="container">
                <div class="products-details-desc">
                    <div class="row align-items-center">
                        <div class="col-lg-6 col-md-6">
                            <div class="main-products-image">
                                <div class="slider slider-nav">
                                    
                                             <?php $__currentLoopData = $SubImages; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $sub): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>  


                                    <div><img src="<?php echo e(URL::to(Storage::url($sub->Image))); ?>" alt="image"></div>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>   
                                </div>

                                <div class="slider slider-for">
                                    
                                       <?php $__currentLoopData = $SubImages; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $sub): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>  
                                    <div><img src="<?php echo e(URL::to(Storage::url($sub->Image))); ?>" alt="image"></div>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>   
             
                                </div>
                            </div>
                        </div>

                        <div class="col-lg-6 col-md-6">
                            <div class="product-content content-two">
                                <h3 style="color: <?php echo e($suben->Txt_Color); ?> !important">      <?php echo e(app()->getLocale() == 'ar' ?$pro->P_Ar_Name :$pro->P_En_Name); ?> </h3>

                                <div class="product-review">
                                    <div class="rating">
                                                        <?php if(empty($pro->rate)): ?>
                                   <i class='bx bx-star'></i>
                                        <i class='bx bx-star'></i>
                                        <i class='bx bx-star'></i>
                                        <i class='bx bx-star'></i>
                                        <i class='bx bx-star'></i>
                                            <?php elseif($pro->rate == 1): ?> 
                                     <i class='bx bxs-star'></i>
                                        <i class='bx bx-star'></i>
                                        <i class='bx bx-star'></i>
                                        <i class='bx bx-star'></i>
                                        <i class='bx bx-star'></i>
                                            <?php elseif($pro->rate == 2): ?>  
                                        <i class='bx bxs-star'></i>
                                        <i class='bx bxs-star'></i>
                                        <i class='bx bx-star'></i>
                                        <i class='bx bx-star'></i>
                                        <i class='bx bx-star'></i>
                                            <?php elseif($pro->rate == 3): ?> 
                                                <i class='bx bxs-star'></i>
                                        <i class='bx bxs-star'></i>
                                        <i class='bx bxs-star'></i>
                                        <i class='bx bx-star'></i>
                                        <i class='bx bx-star'></i>
                                            <?php elseif($pro->rate == 4): ?>  
                                    <i class='bx bxs-star'></i>
                                        <i class='bx bxs-star'></i>
                                        <i class='bx bxs-star'></i>
                                        <i class='bx bxs-star'></i>
                                        <i class='bx bx-star'></i>
                                            <?php elseif($pro->rate == 5): ?>
                                               <i class='bx bxs-star'></i>
                                        <i class='bx bxs-star'></i>
                                        <i class='bx bxs-star'></i>
                                        <i class='bx bxs-star'></i>
                                        <i class='bx bxs-star'></i>
                                            <?php endif; ?>  
                                    </div>
                                </div>

                                <div class="price">
                                    
                                        <?php  $Price=ProductUnits::where('Product',$pro->id)->where('Def',1)->first();   ?>  
                    <input type="hidden" id="CODE" value="<?php echo e($Price->Barcode); ?>">                                
                    <input type="hidden" id="STORE" value="<?php echo e($Ses->Store); ?>">                                
                    <input type="hidden" id="PRODUCT" value="<?php echo e($pro->id); ?>">         
                       <?php if($pro->Offer == 1): ?>
               
                                    
                <span class="old-price"><?php echo e($Ses->Coin()->first()->Symbol); ?>

      <?php echo e(round($Price->Price / $Ses->Coin()->first()->Draw)); ?> </span>
             <span class="new-price" id="PRICE"><?php echo e($Ses->Coin()->first()->Symbol); ?><?php echo e(round($pro->OfferPrice / $Ses->Coin()->first()->Draw)); ?></span>                            
                                    
                            <?php else: ?>
        
                                    
           <span class="new-price" id="PRICE"><?php echo e($Ses->Coin()->first()->Symbol); ?><?php echo e(round($Price->Price / $Ses->Coin()->first()->Draw)); ?></span>                              
                            <?php endif; ?>   
 
                                </div>
        

                                <ul class="products-info">
                <li><span><i style="color: <?php echo e($suben->Title_Color); ?> !important;" class="bx bx-store"></i>:</span> <a href="#" style="color: <?php echo e($suben->Title_Color); ?> !important;" id="In" style="display: none"><?php echo e(trans('admin.In_Stock')); ?></a>  <a href="#" style="color: <?php echo e($suben->Title_Color); ?> !important;" id="Out" style="display: none"><?php echo e(trans('admin.Out_Stock')); ?></a></li>
                                
                                    <li><span><i style="color: <?php echo e($suben->Title_Color); ?> !important;" class="bx bx-closet"></i>:</span> <a style="color: <?php echo e($suben->Title_Color); ?> !important;" href="#" id="StockNum"></a></li>
                                </ul>
                                
            <form method="post" action="<?php echo e(url('AddToCart')); ?>" class="product-form product-form-product-template hidedropdown" enctype="multipart/form-data">
              <?php echo csrf_field(); ?>
                
                                             <input type="hidden" id="P_Code" name="P_Code">  
                                        <input type="hidden"  name="Product" value="<?php echo e($pro->id); ?>">  
                                        
                                        
                                              <?php if($pro->Offer == 1): ?>
         <input type="hidden" id="PriceHide"  name="Price" value="<?php echo e(round($pro->OfferPrice / $Ses->Coin()->first()->Draw)); ?>">                                
      <?php else: ?>
         <input type="hidden" id="PriceHide"  name="Price" value="<?php echo e(round($Price->Price / $Ses->Coin()->first()->Draw)); ?>">                                 
     <?php endif; ?>    

                
                
                                        
                                <?php if($pro->P_Type == 'Single_Variable'): ?>        
                                            
                                      <div class="row">
                                             <div class="col-md-2">
                                        <label style="color: <?php echo e($suben->Title_Color); ?> !important;" class="available-color" for="V1"><?php echo e($NAMEVirable); ?></label>
                                          </div>
                                          
                                   <div class="col-md-10">          
     <select style="color: <?php echo e($suben->Select_Txt_Color); ?> !important; background: <?php echo e($suben->Select_BG_Color); ?> !important;" class="form-control" id="V1">
                                 <option value=""><?php echo e($NAMEVirable); ?></option>
                                     <?php $__currentLoopData = $Viras; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $vir): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($vir->id); ?>">
              <?php echo e(app()->getLocale() == 'ar' ?$vir->Name :$vir->NameEn); ?> 
         </option>  
                                     <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                 </select>
                                          </div>
                                      </div>
                                <?php elseif($pro->P_Type == 'Duble_Variable'): ?>   
                                      
                                             <div class="row">
                                             <div class="col-md-2">
                                        <label style="color: <?php echo e($suben->Title_Color); ?> !important;" class="available-color"><?php echo e($NAMEVirable); ?></label>
                                                 </div>
                                                 
                                                         <div class="col-md-10">
                         <select style="color: <?php echo e($suben->Select_Txt_Color); ?> !important; background: <?php echo e($suben->Select_BG_Color); ?> !important;" class="form-control" id="V1" data-index="option1">
                              
                                     
                                 <option value=""><?php echo e($NAMEVirable); ?></option>
                                     <?php $__currentLoopData = $Viras; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $vir): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($vir->id); ?>"><?php echo e(app()->getLocale() == 'ar' ?$vir->Name :$vir->NameEn); ?> </option>  
                                     <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                     
                                 </select>
                                                 </div>
                                      </div>
                                                  <div class="row">
                                                      
                                                            <div class="col-md-2">
                                        <label style="color: <?php echo e($suben->Title_Color); ?> !important;" class="available-color"><?php echo e($NAMEVirable2); ?></label>
                                                      </div>    
                                                                  <div class="col-md-10">               
                                 <select style="color: <?php echo e($suben->Select_Txt_Color); ?> !important; background: <?php echo e($suben->Select_BG_Color); ?> !important;" class="form-control" id="V2" data-index="option1">
                              
                                     
                                 <option value=""><?php echo e($NAMEVirable2); ?></option>
                                     <?php $__currentLoopData = $Viras2; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $vir): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($vir->id); ?>"><?php echo e(app()->getLocale() == 'ar' ?$vir->Name :$vir->NameEn); ?> </option>  
                                     <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                     
                                 </select>
                                                      </div>
                                      </div>
                                <?php endif; ?>        
            
                

                          



                                <div class="product-quantities">
                                    <span style="color: <?php echo e($suben->Title_Color); ?> !important;">Quantities:</span>

                                    <div class="input-counter">
                                        <span style="background: <?php echo e($suben->Qty_BG_Color); ?> !important;" class="minus-btn  qtyBtn minus" id="MINUS">
                                            <i style="color: <?php echo e($suben->Qty_Txt_Color); ?> !important;" class='bx bx-minus'></i>
                                        </span>
                                   
                <input style="background: <?php echo e($suben->Qty_Input_BG_Color); ?> !important; color: <?php echo e($suben->Qty_Input_Txt_Color); ?> !important"  type="text" id="qty" name="qty" value="1" required>
                                        <span style="background: <?php echo e($suben->Qty_BG_Color); ?> !important;" class="plus-btn qtyBtn plus" id="PLUS">
                                            <i style="color: <?php echo e($suben->Qty_Txt_Color); ?> !important;" class='bx bx-plus'></i>
                                        </span>
                                    </div>
                                </div>

                                <div class="product-add-to-cart"  id="CART" style="display: none">
                           <input type="hidden" id="AVQTY" name="AVQTY">                
                                    <button type="submit" name="add" class="default-btn">
                                        <i class="flaticon-shopping-cart"></i>
                                    <?php echo e(trans('admin.Add_to_Cart')); ?>

                                        <span></span>
                                    </button>
                                </div>
    
                                </form>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="products-details-tabs">
                    <ul class="nav nav-tabs" id="myTab" role="tablist">
                        
                  
                    

 
                        
              <li class="nav-item"><a class="nav-link active" id="description-tab" data-bs-toggle="tab" href="#description" role="tab" aria-controls="description">      <?php echo e(trans('admin.Description')); ?></a></li>
                        <li class="nav-item"><a class="nav-link" id="reviews-tab" data-bs-toggle="tab" href="#speci" role="tab" aria-controls="reviews">    <?php echo e(trans('admin.Specification')); ?></a></li>
                        <li class="nav-item"><a class="nav-link" id="reviews-tab" data-bs-toggle="tab" href="#reviews" role="tab" aria-controls="reviews"><?php echo e(trans('admin.Reviews')); ?></a></li>
               <li class="nav-item"><a class="nav-link" id="information-tab" data-bs-toggle="tab" href="#information" role="tab" aria-controls="information">     <?php echo e(trans('admin.Terms')); ?> &   <?php echo e(trans('admin.Privacy_Policy')); ?></a></li>
                    </ul>

                    <div class="tab-content" id="myTabContent">
                        <div class="tab-pane fade show active" id="description" role="tabpanel">
                            <h2><?php echo e(trans('admin.Description')); ?></h2>
                            
                  <?php echo app()->getLocale() == 'ar' ?$pro->Ar_Desc :$pro->En_Desc; ?>

                        </div>

                        
                          <div class="tab-pane fade" id="speci" role="tabpanel">
                            <h2><?php echo e(trans('admin.Specification')); ?></h2>
                            
                             <?php echo app()->getLocale() == 'ar' ?$pro->Ar_Spec :$pro->En_Spec; ?>

                        </div>
                        
                        <div class="tab-pane fade" id="reviews" role="tabpanel">
                            <div class="products-reviews">
                                <h3><?php echo e(trans('admin.Reviews')); ?></h3>
                                
                                <div class="row">
                                                                         <?php if(!empty(auth()->guard('client')->user()->id)): ?> 
       <?php   $rate=Rate::where('User',auth()->guard('client')->user()->id)->where('Product',$pro->id)->first();  ?>                      
                                                        <?php if(!empty($rate)): ?>
                                        <!-- Edit Rate -->                       
                      <div class="spr-summary">
                                            <form action="<?php echo e(url('EditRate')); ?>" method="post">    
                                                <?php echo csrf_field(); ?>
                                                <input type="hidden" name="ID" value="<?php echo e($rate->id); ?>">
                                                    <input type="hidden" name="Product" value="<?php echo e($pro->id); ?>">
                                                            <span class="product-review">
                                                            
                                                    <div class="rate">
                <input type="radio" id="star5" name="Rate" value="5" <?php if($rate->Rate == 5): ?> checked <?php endif; ?> />
                <label for="star5" title="text">5 stars</label>
                <input type="radio" id="star4" name="Rate" value="4" <?php if($rate->Rate == 4): ?> checked <?php endif; ?>/>
                <label for="star4" title="text">4 stars</label>
                <input type="radio" id="star3" name="Rate" value="3" <?php if($rate->Rate == 3): ?> checked <?php endif; ?>/>
                <label for="star3" title="text">3 stars</label>
                <input type="radio" id="star2" name="Rate" value="2" <?php if($rate->Rate == 2): ?> checked <?php endif; ?>/>
                <label for="star2" title="text">2 stars</label>
                <input type="radio" id="star1" name="Rate" value="1" <?php if($rate->Rate == 1): ?> checked <?php endif; ?>/>
                <label for="star1" title="text">1 star</label>
                            </div>
                                                                
                                                            </span>
                                                            <span class="spr-summary-actions">
                      <button type="submit" class="spr-summary-actions-newreview btn"><i class="bx bx-check"></i></button>
                                                            </span>
                                            </form>    
                                                        </div>                                     
                                                        <?php else: ?>                                    
                                                          <!-- Add Rate -->  
                                                        <div class="spr-summary">
                                            <form action="<?php echo e(url('AddRate')); ?>" method="post">    
                                                <?php echo csrf_field(); ?>
                                                <input type="hidden" name="Product" value="<?php echo e($pro->id); ?>">
                                                            <span class="product-review">
                                                            
                                                    <div class="rate">
                <input type="radio" id="star5" name="Rate" value="5" />
                <label for="star5" title="text">5 stars</label>
                <input type="radio" id="star4" name="Rate" value="4" />
                <label for="star4" title="text">4 stars</label>
                <input type="radio" id="star3" name="Rate" value="3" />
                <label for="star3" title="text">3 stars</label>
                <input type="radio" id="star2" name="Rate" value="2" />
                <label for="star2" title="text">2 stars</label>
                <input type="radio" id="star1" name="Rate" value="1" />
                <label for="star1" title="text">1 star</label>
                            </div>
                                                                
                                                            </span>
                                                            <span class="spr-summary-actions">
                      <button type="submit" class="spr-summary-actions-newreview btn"><i class="bx bx-check"></i></button>
                                                            </span>
                                            </form>    
                                                        </div> 
                                                    <?php endif; ?>    
                                                    <?php endif; ?>    
                                </div>
                            </div>

                            <div class="products-review-form">

                                <div class="review-comments">
                             <?php $__currentLoopData = $Comments; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $com): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>              
                                    <div class="review-item">
                                        <h3><?php echo e($com->User()->first()->Name); ?></h3>
                                        <span> <strong>      <?php echo e($com->Date); ?>   </strong></span>
                                        <p><?php echo e($com->Comment); ?></p>
                                        
                                                  <?php if(!empty(auth()->guard('client')->user()->id)): ?>  
   <?php if($com->User == auth()->guard('client')->user()->id): ?>  
                <div class="row">                                           
              <a href="<?php echo e(url('DeleteComment/'.$com->id)); ?>" class="btn btn-danger col-md-1 m-2"><i class="bx bxs-trash"></i></a>  
                   
      <a class="btn btn-primary col-md-1 m-2" type="button" data-bs-toggle="collapse" data-bs-target="#multiCollapseExample<?php echo e($com->id); ?>" aria-expanded="false" aria-controls="multiCollapseExample<?php echo e($com->id); ?>">
  <i class="bx bx-edit-alt"></i>
  </a>                
                    
                </div>

<div class="row">
  <div class="col">
    <div class="collapse multi-collapse" id="multiCollapseExample<?php echo e($com->id); ?>">
      <div class="card card-body">
      
          
          
               <form method="post" action="<?php echo e(url('EditComment')); ?>">
                                           <?php echo csrf_field(); ?>
                 <input type="hidden" name="ID" value="<?php echo e($com->id); ?>">          
                                        <div class="row">
                                         <input type="hidden" name="Product" value="<?php echo e($pro->id); ?>"> 
                                            <div class="col-lg-12 col-md-12">
                                                <div class="form-group">
                                                    <textarea  name="Comment" id="review-body" cols="30" rows="6" placeholder="<?php echo e(trans('admin.Write_Comment')); ?>" class="form-control" required>      <?php echo e($com->Comment); ?></textarea>
                                                </div>
                                            </div>

                                            <div class="col-lg-12 col-md-12">
                                                <button type="submit" class="default-btn">
                                               <?php echo e(trans('admin.Edit')); ?>

                                                    <span></span>
                                                </button>
                                            </div>
                                        </div>
                                    </form>
      </div>
    </div>
  </div>

</div>                                            
                     <?php endif; ?> 
                        <?php endif; ?>                   

                                    </div>                
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                              
                                </div>
           <?php if(!empty(auth()->guard('client')->user()->id)): ?>  

                                <div class="review-form">
                                    <h3><?php echo e(trans('admin.Write_Comment')); ?></h3>

                                    <form method="post" action="<?php echo e(url('AddComment')); ?>">
                                           <?php echo csrf_field(); ?>
                                        <div class="row">
                                         <input type="hidden" name="Product" value="<?php echo e($pro->id); ?>"> 
                                            <div class="col-lg-12 col-md-12">
                                                <div class="form-group">
                                                    <textarea  name="Comment" id="review-body" cols="30" rows="6" placeholder="<?php echo e(trans('admin.Write_Comment')); ?>" class="form-control" required></textarea>
                                                </div>
                                            </div>

                                            <div class="col-lg-12 col-md-12">
                                                <button type="submit" class="default-btn">
                                               <?php echo e(trans('admin.Submit')); ?>

                                                    <span></span>
                                                </button>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                                
                                <?php endif; ?>
                            </div>
                        </div>

                        <div class="tab-pane fade show" id="information" role="tabpanel">
                            
                <?php echo app()->getLocale() == 'ar' ? $Terms->Arabic_Desc :$Terms->English_Desc; ?>            
                        <hr>
                            
                      <?php echo app()->getLocale() == 'ar' ? $Polices->Arabic_Desc :$Polices->English_Desc; ?>                

                        </div>
                    </div>
                </div>
            </div>
        </section>
        <!-- End Products Details Area -->

        



        <!-- Start Arrivals Products Area -->
        <section class="arrivals-products-area pt-50 pb-20">
            <div class="container">
                <div class="section-title">
                    <h2><?php echo e(trans('admin.Related_Product')); ?></h2>
                </div>

                <div class="row first owl-carousel owl-theme">
                    
                    
                               <?php $__currentLoopData = $ProductGroups; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $XX): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    
                    <div class="col-lg-12 item col-sm-12">
                        <div class="single-arrivals-products">
                            <div class="arrivals-products-image">
                                <a href="<?php echo e(url('ProductDetails/'.$XX->id)); ?>"><img src="<?php echo e(URL::to($XX->Image)); ?>" alt="image"></a>
                                <div class="tag">        <?php echo e(app()->getLocale() == 'ar' ?$XX->Group()->first()->Name :$XX->Group()->first()->NameEn); ?>    </div>
                                <ul class="arrivals-action">
                                      <li>
                                        <a href="<?php echo e(url('ProductDetails/'.$XX->id)); ?>">
                                            <i class="flaticon-shopping-cart"></i>
                                        </a>
                                    </li>
                                    
                                   <?php if(!empty(auth()->guard('client')->user()->id)): ?>            
                                    <?php            
                                       $Wish=Wishlist::where('Product',$XX->id)
                                            ->where('User',auth()->guard('client')->user()->id)->first(); 
                                       
                                                       $Comp=Compare::where('Product',$XX->id)
                                            ->where('User',auth()->guard('client')->user()->id)->first(); 
                                                
                                    ?>
          
                                    <li>
                                        
                                        <?php if(empty($Wish)): ?>     
                                        <a href="<?php echo e(url('AddWish/'.$XX->id)); ?>"><i class="flaticon-heart"></i></a>
                                             <?php else: ?>   
                                       
                                        <i class="bx bxs-heart"></i>
                                            <?php endif; ?> 
                                    </li>
                                    <li>
                                       <?php if(empty($Comp)): ?>      
                                        <a href="<?php echo e(url('AddCompare/'.$XX->id)); ?>"><i class="bx bx-git-compare"></i></a>
                                        <?php else: ?>
                                        
                                        <i class="bx bx-git-compare">  <i class="bx bx-badge-check"></i></i>
                                      
                                          <?php endif; ?>  
                                    </li>
                          <?php endif; ?>              
                                </ul>
                            </div>

                            <div class="arrivals-products-content">
                                <h3>
                                    <a href="<?php echo e(url('ProductDetails/'.$XX->id)); ?>">   <?php echo e(app()->getLocale() == 'ar' ?$XX->P_Ar_Name :$XX->P_En_Name); ?>     </a>
                                </h3>
                                <ul class="rating">
                                     <?php if(empty($XX->rate)): ?>
                                                          <li><i class='bx bx-star'></i></li>
                                    <li><i class='bx bx-star'></i></li>
                                    <li><i class='bx bx-star'></i></li>
                                    <li><i class='bx bx-star'></i></li>
                                    <li><i class='bx bx-star'></i></li>
                                            <?php elseif($XX->rate == 1): ?> 
                                                          <li><i class='bx bxs-star'></i></li>
                                    <li><i class='bx bx-star'></i></li>
                                    <li><i class='bx bx-star'></i></li>
                                    <li><i class='bx bx-star'></i></li>
                                    <li><i class='bx bx-star'></i></li>
                                            <?php elseif($XX->rate == 2): ?>  
                                                 <li><i class='bx bxs-star'></i></li>
                                    <li><i class='bx bxs-star'></i></li>
                                    <li><i class='bx bx-star'></i></li>
                                    <li><i class='bx bx-star'></i></li>
                                    <li><i class='bx bx-star'></i></li>
                                            <?php elseif($XX->rate == 3): ?> 
                                                           <li><i class='bx bxs-star'></i></li>
                                    <li><i class='bx bxs-star'></i></li>
                                    <li><i class='bx bxs-star'></i></li>
                                    <li><i class='bx bx-star'></i></li>
                                    <li><i class='bx bx-star'></i></li>
                                            <?php elseif($XX->rate == 4): ?>  
                                                               <li><i class='bx bxs-star'></i></li>
                                    <li><i class='bx bxs-star'></i></li>
                                    <li><i class='bx bxs-star'></i></li>
                                    <li><i class='bx bxs-star'></i></li>
                                    <li><i class='bx bx-star'></i></li>
                                            <?php elseif($XX->rate == 5): ?>
                                                             <li><i class='bx bxs-star'></i></li>
                                    <li><i class='bx bxs-star'></i></li>
                                    <li><i class='bx bxs-star'></i></li>
                                    <li><i class='bx bxs-star'></i></li>
                                    <li><i class='bx bxs-star'></i></li>
                                            <?php endif; ?>  
                                </ul>
                                                   <span>
                                
     <?php  $Price=ProductUnits::where('Product',$XX->id)->where('Def',1)->first();   ?>  
     <?php if($XX->Offer == 1): ?>
<del><?php echo e($Ses->Coin()->first()->Symbol); ?><?php echo e(round($Price->Price / $Ses->Coin()->first()->Draw)); ?></del>
<?php echo e($Ses->Coin()->first()->Symbol); ?><?php echo e(round($pro->OfferPrice / $Ses->Coin()->first()->Draw)); ?>

        <?php else: ?>
<?php echo e($Ses->Coin()->first()->Symbol); ?><?php echo e(round($Price->Price / $Ses->Coin()->first()->Draw)); ?>

        <?php endif; ?>     
                                
                                
                                </span>
                            </div>
                        </div>
                    </div>

                 <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            </div>
        </section>
        <!-- End Arrivals Products Area -->


<?php $__env->stopSection(); ?>

    <style>

.rate {
    float: left;
    height: 46px;
    padding: 0 10px;
}
.rate:not(:checked) > input {
    position:absolute;
    top:-9999px;
}
.rate:not(:checked) > label {
    float:right;
    width:1em;
    overflow:hidden;
    white-space:nowrap;
    cursor:pointer;
    font-size:30px;
    color:#ccc;
}
.rate:not(:checked) > label:before {
    content: '★ ';
}
.rate > input:checked ~ label {
    color: <?php echo e($suben->Rate_Color); ?> !important;   
}
.rate:not(:checked) > label:hover,
.rate:not(:checked) > label:hover ~ label {
    color: <?php echo e($suben->Rate_Color); ?> !important;  
}
.rate > input:checked + label:hover,
.rate > input:checked + label:hover ~ label,
.rate > input:checked ~ label:hover,
.rate > input:checked ~ label:hover ~ label,
.rate > label:hover ~ input:checked ~ label {
    color: <?php echo e($suben->Rate_Color); ?> !important;
}    

</style> 



<?php $__env->startPush('js'); ?>

   <!-- Photoswipe Gallery -->
     <script src="<?php echo e(asset('Front/assets/js/vendor/photoswipe.min.js')); ?>"></script>
     <script src="<?php echo e(asset('Front/assets/js/vendor/photoswipe-ui-default.min.js')); ?>"></script>
     <script>
        $(function(){
            var $pswp = $('.pswp')[0],
                image = [],
                getItems = function() {
                    var items = [];
                    $('.lightboximages a').each(function() {
                        var $href   = $(this).attr('href'),
                            $size   = $(this).data('size').split('x'),
                            item = {
                                src : $href,
                                w: $size[0],
                                h: $size[1]
                            }
                            items.push(item);
                    });
                    return items;
                }
            var items = getItems();
        
            $.each(items, function(index, value) {
                image[index]     = new Image();
                image[index].src = value['src'];
            });
            $('.prlightbox').on('click', function (event) {
                event.preventDefault();
              
                var $index = $(".active-thumb").parent().attr('data-slick-index');
                $index++;
                $index = $index-1;
        
                var options = {
                    index: $index,
                    bgOpacity: 0.9,
                    showHideOpacity: true
                }
                var lightBox = new PhotoSwipe($pswp, PhotoSwipeUI_Default, items, options);
                lightBox.init();
            });
        });
        </script>

<!-- Filter Completed Item -->
<?php if($pro->P_Type == 'Completed'): ?>   
<script>
     $(document).ready(function() {

   
   var CODE = $('#CODE').val();
   var STORE = $('#STORE').val();
   var PRODUCT = $('#PRODUCT').val();
   var Quantity = $('#qty').val();


                         $.ajax({
                             url: '1/SiteProQty',
                             type:"GET",
                             data:{
                                 CODE:CODE,
                                 STORE:STORE,
                                 PRODUCT:PRODUCT,  
                             },
                             dataType:"json",
                             beforeSend: function(){
                                 $('#loader').css("visibility", "visible");
                             },
   
                             success:function(data) {
                            $('#P_Code').val(data.code);
                     $('#AVQTY').val(data.qty);
                                     if(parseFloat(data.qty) == 0){
                            document.getElementById('StockNum').style.display='none';             
                            document.getElementById('In').style.display='none';             
                            document.getElementById('CART').style.display='none';             
                            document.getElementById('Out').style.display='block';       
                                     }else{
                                         
                                if(parseFloat(Quantity) > parseFloat(data.qty) || parseFloat(Quantity) == 0){
                                   
                         document.getElementById('StockNum').style.display='block';             
                            document.getElementById('In').style.display='none';             
                            document.getElementById('CART').style.display='none';             
                            document.getElementById('Out').style.display='block';                       
                                    
                                    
                                }else{
                              $('#StockNum').text(data.qty);
                             
                            document.getElementById('StockNum').style.display='block';             
                            document.getElementById('In').style.display='block';             
                            document.getElementById('CART').style.display='block';             
                            document.getElementById('Out').style.display='none';                
                                    
                                }         
                                         
                                       
                                     }

                               
      
   
                             },
                             complete: function(){
                                 $('#loader').css("visibility", "hidden");
                             }
                         });
                
  
   
   });  
    
     $('#MINUS').on('click', function(){
         
   var CODE = $('#CODE').val();
   var STORE = $('#STORE').val();
   var PRODUCT = $('#PRODUCT').val();
   var Quantity = $('#qty').val();



                         $.ajax({
                             url: '1/SiteProQty',
                             type:"GET",
                             data:{
                                 CODE:CODE,
                                 STORE:STORE,
                                 PRODUCT:PRODUCT,  
                             },
                             dataType:"json",
                             beforeSend: function(){
                                 $('#loader').css("visibility", "visible");
                             },
   
                             success:function(data) {
                  $('#AVQTY').val(data.qty);
                                     $('#P_Code').val(data.code);
                                     if(parseFloat(data.qty) == 0){
                            document.getElementById('StockNum').style.display='none';             
                            document.getElementById('In').style.display='none';             
                            document.getElementById('CART').style.display='none';             
                            document.getElementById('Out').style.display='block';       
                                     }else{
                                         
                                if(parseFloat(Quantity) > parseFloat(data.qty) || parseFloat(Quantity) == 0){
                                   
                         document.getElementById('StockNum').style.display='block';             
                            document.getElementById('In').style.display='none';             
                            document.getElementById('CART').style.display='none';             
                            document.getElementById('Out').style.display='block';                       
                                    
                                    
                                }else{
                              $('#StockNum').text(data.qty);
                            document.getElementById('StockNum').style.display='block';             
                            document.getElementById('In').style.display='block';             
                            document.getElementById('CART').style.display='block';             
                            document.getElementById('Out').style.display='none';                
                                    
                                }         
                                         
                                       
                                     }

                               
      
   
                             },
                             complete: function(){
                                 $('#loader').css("visibility", "hidden");
                             }
                         });
                
  


         
     });
    
    
        $('#PLUS').on('click', function(){
          var CODE = $('#CODE').val();
   var STORE = $('#STORE').val();
   var PRODUCT = $('#PRODUCT').val();
   var Quantity = $('#qty').val();


                         $.ajax({
                             url: '1/SiteProQty',
                             type:"GET",
                             data:{
                                 CODE:CODE,
                                 STORE:STORE,
                                 PRODUCT:PRODUCT,  
                             },
                             dataType:"json",
                             beforeSend: function(){
                                 $('#loader').css("visibility", "visible");
                             },
   
                             success:function(data) {
                $('#AVQTY').val(data.qty);
                                     $('#P_Code').val(data.code);
                                     if(parseFloat(data.qty) == 0){
                            document.getElementById('StockNum').style.display='none';             
                            document.getElementById('In').style.display='none';             
                            document.getElementById('CART').style.display='none';             
                            document.getElementById('Out').style.display='block';       
                                     }else{
                                         
                                if(parseFloat(Quantity) > parseFloat(data.qty) || parseFloat(Quantity) == 0){
                                   
                         document.getElementById('StockNum').style.display='block';             
                            document.getElementById('In').style.display='none';             
                            document.getElementById('CART').style.display='none';             
                            document.getElementById('Out').style.display='block';                       
                                    
                                    
                                }else{
                              $('#StockNum').text(data.qty);
                            document.getElementById('StockNum').style.display='block';             
                            document.getElementById('In').style.display='block';             
                            document.getElementById('CART').style.display='block';             
                            document.getElementById('Out').style.display='none';                
                                    
                                }         
                                         
                                       
                                     }

                               
      
   
                             },
                             complete: function(){
                                 $('#loader').css("visibility", "hidden");
                             }
                         });
                
    

         
     });
    
        $('#qty').on('click', function(){
         
   var CODE = $('#CODE').val();
   var STORE = $('#STORE').val();
   var PRODUCT = $('#PRODUCT').val();
   var Quantity = $('#qty').val();


                         $.ajax({
                             url: '1/SiteProQty',
                             type:"GET",
                             data:{
                                 CODE:CODE,
                                 STORE:STORE,
                                 PRODUCT:PRODUCT,  
                             },
                             dataType:"json",
                             beforeSend: function(){
                                 $('#loader').css("visibility", "visible");
                             },
   
                             success:function(data) {
                    $('#AVQTY').val(data.qty);
                        $('#P_Code').val(data.code);
                                     if(parseFloat(data.qty) == 0){
                            document.getElementById('StockNum').style.display='none';             
                            document.getElementById('In').style.display='none';             
                            document.getElementById('CART').style.display='none';             
                            document.getElementById('Out').style.display='block';       
                                     }else{
                                         
                                if(parseFloat(Quantity) > parseFloat(data.qty) || parseFloat(Quantity) == 0){
                                   
                         document.getElementById('StockNum').style.display='block';             
                            document.getElementById('In').style.display='none';             
                            document.getElementById('CART').style.display='none';             
                            document.getElementById('Out').style.display='block';                       
                                    
                                    
                                }else{
                              $('#StockNum').text(data.qty);
                            document.getElementById('StockNum').style.display='block';             
                            document.getElementById('In').style.display='block';             
                            document.getElementById('CART').style.display='block';             
                            document.getElementById('Out').style.display='none';                
                                    
                                }         
                                         
                                       
                                     }

                               
      
   
                             },
                             complete: function(){
                                 $('#loader').css("visibility", "hidden");
                             }
                         });
                
  

         
     }); 
    
       $('#qty').on('keyup', function(){
     
   var CODE = $('#CODE').val();
   var STORE = $('#STORE').val();
   var PRODUCT = $('#PRODUCT').val();
   var Quantity = $('#qty').val();


                         $.ajax({
                             url: '1/SiteProQty',
                             type:"GET",
                             data:{
                                 CODE:CODE,
                                 STORE:STORE,
                                 PRODUCT:PRODUCT,  
                             },
                             dataType:"json",
                             beforeSend: function(){
                                 $('#loader').css("visibility", "visible");
                             },
   
                             success:function(data) {
                       $('#AVQTY').val(data.qty);
                                     $('#P_Code').val(data.code);
                                     if(parseFloat(data.qty) == 0){
                            document.getElementById('StockNum').style.display='none';             
                            document.getElementById('In').style.display='none';             
                            document.getElementById('CART').style.display='none';             
                            document.getElementById('Out').style.display='block';       
                                     }else{
                                         
                                if(parseFloat(Quantity) > parseFloat(data.qty) || parseFloat(Quantity) == 0){
                                   
                         document.getElementById('StockNum').style.display='block';             
                            document.getElementById('In').style.display='none';             
                            document.getElementById('CART').style.display='none';             
                            document.getElementById('Out').style.display='block';                       
                                    
                                    
                                }else{
                              $('#StockNum').text(data.qty);
                            document.getElementById('StockNum').style.display='block';             
                            document.getElementById('In').style.display='block';             
                            document.getElementById('CART').style.display='block';             
                            document.getElementById('Out').style.display='none';                
                                    
                                }         
                                         
                                       
                                     }

                               
      
   
                             },
                             complete: function(){
                                 $('#loader').css("visibility", "hidden");
                             }
                         });
                
  
         
     }); 
        $('#qty').on('keypress', function(){
         
    var CODE = $('#CODE').val();
   var STORE = $('#STORE').val();
   var PRODUCT = $('#PRODUCT').val();
   var Quantity = $('#qty').val();


                         $.ajax({
                             url: '1/SiteProQty',
                             type:"GET",
                             data:{
                                 CODE:CODE,
                                 STORE:STORE,
                                 PRODUCT:PRODUCT,  
                             },
                             dataType:"json",
                             beforeSend: function(){
                                 $('#loader').css("visibility", "visible");
                             },
   
                             success:function(data) {
                      $('#AVQTY').val(data.qty);
                        $('#P_Code').val(data.code);             
                                     if(parseFloat(data.qty) == 0){
                            document.getElementById('StockNum').style.display='none';             
                            document.getElementById('In').style.display='none';             
                            document.getElementById('CART').style.display='none';             
                            document.getElementById('Out').style.display='block';       
                                     }else{
                                         
                                if(parseFloat(Quantity) > parseFloat(data.qty) || parseFloat(Quantity) == 0){
                                   
                         document.getElementById('StockNum').style.display='block';             
                            document.getElementById('In').style.display='none';             
                            document.getElementById('CART').style.display='none';             
                            document.getElementById('Out').style.display='block';                       
                                    
                                    
                                }else{
                              $('#StockNum').text(data.qty);
                            document.getElementById('StockNum').style.display='block';             
                            document.getElementById('In').style.display='block';             
                            document.getElementById('CART').style.display='block';             
                            document.getElementById('Out').style.display='none';                
                                    
                                }         
                                         
                                       
                                     }

                               
      
   
                             },
                             complete: function(){
                                 $('#loader').css("visibility", "hidden");
                             }
                         });
                
  
         
     }); 
    
</script>  
<?php endif; ?>

<!-- Filter Single_Variable Item -->
<?php if($pro->P_Type == 'Single_Variable'): ?>   
<script>
      $('#V1').on('change', function(){
         
   var V = $('#V1').val();
   var STORE = $('#STORE').val();
   var PRODUCT = $('#PRODUCT').val();
   var Quantity = $('#qty').val();



                         $.ajax({
                             url: '1/SiteProQtyV',
                             type:"GET",
                             data:{
                                 V:V,
                                 STORE:STORE,
                                 PRODUCT:PRODUCT,  
                             },
                             dataType:"json",
                             beforeSend: function(){
                                 $('#loader').css("visibility", "visible");
                             },
   
                             success:function(data) {
                  $('#P_Code').val(data.code);
                  $('#AVQTY').val(data.qty);
                                 
                        if(parseFloat(data.price) != 0){         
                  $('#PRICE').text(data.price);
                  $('#PriceHide').val(data.price);
                        }
                                     if(parseFloat(data.qty) == 0){
                            document.getElementById('StockNum').style.display='none';             
                            document.getElementById('In').style.display='none';             
                            document.getElementById('CART').style.display='none';             
                            document.getElementById('Out').style.display='block';       
                                     }else{
                                         
                                if(parseFloat(Quantity) > parseFloat(data.qty) || parseFloat(Quantity) == 0){
                                   
                         document.getElementById('StockNum').style.display='block';             
                            document.getElementById('In').style.display='none';             
                            document.getElementById('CART').style.display='none';             
                            document.getElementById('Out').style.display='block';                       
                                    
                                    
                                }else{
                              $('#StockNum').text(data.qty);
                            document.getElementById('StockNum').style.display='block';             
                            document.getElementById('In').style.display='block';             
                            document.getElementById('CART').style.display='block';             
                            document.getElementById('Out').style.display='none';                
                                    
                                }         
                                         
                                       
                                     }

                               
      
   
                             },
                             complete: function(){
                                 $('#loader').css("visibility", "hidden");
                             }
                         });
                
  


         
     }); 
    
         $('#MINUS').on('click', function(){
            var V = $('#V1').val();
   var STORE = $('#STORE').val();
   var PRODUCT = $('#PRODUCT').val();
   var Quantity = $('#qty').val();



                         $.ajax({
                             url: '1/SiteProQtyV',
                             type:"GET",
                             data:{
                                 V:V,
                                 STORE:STORE,
                                 PRODUCT:PRODUCT,  
                             },
                             dataType:"json",
                             beforeSend: function(){
                                 $('#loader').css("visibility", "visible");
                             },
   
                             success:function(data) {
                  $('#P_Code').val(data.code);
                  $('#AVQTY').val(data.qty);
                   if(parseFloat(data.price) != 0){         
                  $('#PRICE').text(data.price);
                     $('#PriceHide').val(data.price);   
                        }
                                     if(parseFloat(data.qty) == 0){
                            document.getElementById('StockNum').style.display='none';             
                            document.getElementById('In').style.display='none';             
                            document.getElementById('CART').style.display='none';             
                            document.getElementById('Out').style.display='block';       
                                     }else{
                                         
                                if(parseFloat(Quantity) > parseFloat(data.qty) || parseFloat(Quantity) == 0){
                                   
                         document.getElementById('StockNum').style.display='block';             
                            document.getElementById('In').style.display='none';             
                            document.getElementById('CART').style.display='none';             
                            document.getElementById('Out').style.display='block';                       
                                    
                                    
                                }else{
                              $('#StockNum').text(data.qty);
                            document.getElementById('StockNum').style.display='block';             
                            document.getElementById('In').style.display='block';             
                            document.getElementById('CART').style.display='block';             
                            document.getElementById('Out').style.display='none';                
                                    
                                }         
                                         
                                       
                                     }

                               
      
   
                             },
                             complete: function(){
                                 $('#loader').css("visibility", "hidden");
                             }
                         });
                
  

   
             
         });
     $('#PLUS').on('click', function(){
          var V = $('#V1').val();
   var STORE = $('#STORE').val();
   var PRODUCT = $('#PRODUCT').val();
   var Quantity = $('#qty').val();



                         $.ajax({
                             url: '1/SiteProQtyV',
                             type:"GET",
                             data:{
                                 V:V,
                                 STORE:STORE,
                                 PRODUCT:PRODUCT,  
                             },
                             dataType:"json",
                             beforeSend: function(){
                                 $('#loader').css("visibility", "visible");
                             },
   
                             success:function(data) {
                  $('#P_Code').val(data.code);
                  $('#AVQTY').val(data.qty);
                  if(parseFloat(data.price) != 0){         
                  $('#PRICE').text(data.price);
                     $('#PriceHide').val(data.price);  
                        }            
                                     if(parseFloat(data.qty) == 0){
                            document.getElementById('StockNum').style.display='none';             
                            document.getElementById('In').style.display='none';             
                            document.getElementById('CART').style.display='none';             
                            document.getElementById('Out').style.display='block';       
                                     }else{
                                         
                                if(parseFloat(Quantity) > parseFloat(data.qty) || parseFloat(Quantity) == 0){
                                   
                         document.getElementById('StockNum').style.display='block';             
                            document.getElementById('In').style.display='none';             
                            document.getElementById('CART').style.display='none';             
                            document.getElementById('Out').style.display='block';                       
                                    
                                    
                                }else{
                              $('#StockNum').text(data.qty);
                            document.getElementById('StockNum').style.display='block';             
                            document.getElementById('In').style.display='block';             
                            document.getElementById('CART').style.display='block';             
                            document.getElementById('Out').style.display='none';                
                                    
                                }         
                                         
                                       
                                     }

                               
      
   
                             },
                             complete: function(){
                                 $('#loader').css("visibility", "hidden");
                             }
                         });
                
  

 
         
     });
      $('#qty').on('click', function(){
          var V = $('#V1').val();
   var STORE = $('#STORE').val();
   var PRODUCT = $('#PRODUCT').val();
   var Quantity = $('#qty').val();



                         $.ajax({
                             url: '1/SiteProQtyV',
                             type:"GET",
                             data:{
                                 V:V,
                                 STORE:STORE,
                                 PRODUCT:PRODUCT,  
                             },
                             dataType:"json",
                             beforeSend: function(){
                                 $('#loader').css("visibility", "visible");
                             },
   
                             success:function(data) {
                  $('#P_Code').val(data.code);
                  $('#AVQTY').val(data.qty);
            if(parseFloat(data.price) != 0){         
                  $('#PRICE').text(data.price);
             $('#PriceHide').val(data.price);    
                        }         
                                     if(parseFloat(data.qty) == 0){
                            document.getElementById('StockNum').style.display='none';             
                            document.getElementById('In').style.display='none';             
                            document.getElementById('CART').style.display='none';             
                            document.getElementById('Out').style.display='block';       
                                     }else{
                                         
                                if(parseFloat(Quantity) > parseFloat(data.qty) || parseFloat(Quantity) == 0){
                                   
                         document.getElementById('StockNum').style.display='block';             
                            document.getElementById('In').style.display='none';             
                            document.getElementById('CART').style.display='none';             
                            document.getElementById('Out').style.display='block';                       
                                    
                                    
                                }else{
                              $('#StockNum').text(data.qty);
                            document.getElementById('StockNum').style.display='block';             
                            document.getElementById('In').style.display='block';             
                            document.getElementById('CART').style.display='block';             
                            document.getElementById('Out').style.display='none';                
                                    
                                }         
                                         
                                       
                                     }

                               
      
   
                             },
                             complete: function(){
                                 $('#loader').css("visibility", "hidden");
                             }
                         });
                
  

  
          
      });
  $('#qty').on('keyup', function(){
        var V = $('#V1').val();
   var STORE = $('#STORE').val();
   var PRODUCT = $('#PRODUCT').val();
   var Quantity = $('#qty').val();



                         $.ajax({
                             url: '1/SiteProQtyV',
                             type:"GET",
                             data:{
                                 V:V,
                                 STORE:STORE,
                                 PRODUCT:PRODUCT,  
                             },
                             dataType:"json",
                             beforeSend: function(){
                                 $('#loader').css("visibility", "visible");
                             },
   
                             success:function(data) {
                  $('#P_Code').val(data.code);
                  $('#AVQTY').val(data.qty);
                 if(parseFloat(data.price) != 0){         
                  $('#PRICE').text(data.price);
                 $('#PriceHide').val(data.price);     
                        }         
                                     if(parseFloat(data.qty) == 0){
                            document.getElementById('StockNum').style.display='none';             
                            document.getElementById('In').style.display='none';             
                            document.getElementById('CART').style.display='none';             
                            document.getElementById('Out').style.display='block';       
                                     }else{
                                         
                                if(parseFloat(Quantity) > parseFloat(data.qty) || parseFloat(Quantity) == 0){
                                   
                         document.getElementById('StockNum').style.display='block';             
                            document.getElementById('In').style.display='none';             
                            document.getElementById('CART').style.display='none';             
                            document.getElementById('Out').style.display='block';                       
                                    
                                    
                                }else{
                              $('#StockNum').text(data.qty);
                            document.getElementById('StockNum').style.display='block';             
                            document.getElementById('In').style.display='block';             
                            document.getElementById('CART').style.display='block';             
                            document.getElementById('Out').style.display='none';                
                                    
                                }         
                                         
                                       
                                     }

                               
      
   
                             },
                             complete: function(){
                                 $('#loader').css("visibility", "hidden");
                             }
                         });
                
  


      
  });
    $('#qty').on('keypress', function(){
        var V = $('#V1').val();
   var STORE = $('#STORE').val();
   var PRODUCT = $('#PRODUCT').val();
   var Quantity = $('#qty').val();



                         $.ajax({
                             url: '1/SiteProQtyV',
                             type:"GET",
                             data:{
                                 V:V,
                                 STORE:STORE,
                                 PRODUCT:PRODUCT,  
                             },
                             dataType:"json",
                             beforeSend: function(){
                                 $('#loader').css("visibility", "visible");
                             },
   
                             success:function(data) {
                  $('#P_Code').val(data.code);
                  $('#AVQTY').val(data.qty);
           if(parseFloat(data.price) != 0){         
                  $('#PRICE').text(data.price);
             $('#PriceHide').val(data.price);   
                        }                
                                     if(parseFloat(data.qty) == 0){
                            document.getElementById('StockNum').style.display='none';             
                            document.getElementById('In').style.display='none';             
                            document.getElementById('CART').style.display='none';             
                            document.getElementById('Out').style.display='block';       
                                     }else{
                                         
                                if(parseFloat(Quantity) > parseFloat(data.qty) || parseFloat(Quantity) == 0){
                                   
                         document.getElementById('StockNum').style.display='block';             
                            document.getElementById('In').style.display='none';             
                            document.getElementById('CART').style.display='none';             
                            document.getElementById('Out').style.display='block';                       
                                    
                                    
                                }else{
                              $('#StockNum').text(data.qty);
                            document.getElementById('StockNum').style.display='block';             
                            document.getElementById('In').style.display='block';             
                            document.getElementById('CART').style.display='block';             
                            document.getElementById('Out').style.display='none';                
                                    
                                }         
                                         
                                       
                                     }

                               
      
   
                             },
                             complete: function(){
                                 $('#loader').css("visibility", "hidden");
                             }
                         });
                
  

  
        
    }); 
    
</script>
<?php endif; ?>

<!-- Filter Duble_Variable Item -->
<?php if($pro->P_Type == 'Duble_Variable'): ?>   
<script>
       $('#V1').on('change', function(){
         
   var V = $('#V1').val();
   var VV = $('#V2').val();
   var STORE = $('#STORE').val();
   var PRODUCT = $('#PRODUCT').val();
   var Quantity = $('#qty').val();


if(VV != ''){
                         $.ajax({
                             url: '1/SiteProQtyVV',
                             type:"GET",
                             data:{
                                 VV:VV,
                                 V:V,
                                 STORE:STORE,
                                 PRODUCT:PRODUCT,  
                             },
                             dataType:"json",
                             beforeSend: function(){
                                 $('#loader').css("visibility", "visible");
                             },
   
                             success:function(data) {
                  $('#P_Code').val(data.code);
                  $('#AVQTY').val(data.qty);
                 if(parseFloat(data.price) != 0){         
                  $('#PRICE').text(data.price);
                     $('#PriceHide').val(data.price); 
                        }        
                                     if(parseFloat(data.qty) == 0){
                            document.getElementById('StockNum').style.display='none';             
                            document.getElementById('In').style.display='none';             
                            document.getElementById('CART').style.display='none';             
                            document.getElementById('Out').style.display='block';       
                                     }else{
                                         
                                if(parseFloat(Quantity) > parseFloat(data.qty) || parseFloat(Quantity) == 0){
                                   
                         document.getElementById('StockNum').style.display='block';             
                            document.getElementById('In').style.display='none';             
                            document.getElementById('CART').style.display='none';             
                            document.getElementById('Out').style.display='block';                       
                                    
                                    
                                }else{
                              $('#StockNum').text(data.qty);
                            document.getElementById('StockNum').style.display='block';             
                            document.getElementById('In').style.display='block';             
                            document.getElementById('CART').style.display='block';             
                            document.getElementById('Out').style.display='none';                
                                    
                                }         
                                         
                                       
                                     }

                               
      
   
                             },
                             complete: function(){
                                 $('#loader').css("visibility", "hidden");
                             }
                         });
                
}


         
     }); 
     $('#V2').on('change', function(){
         
   var V = $('#V1').val();
   var VV = $('#V2').val();
   var STORE = $('#STORE').val();
   var PRODUCT = $('#PRODUCT').val();
   var Quantity = $('#qty').val();


if(V != ''){
                         $.ajax({
                             url: '1/SiteProQtyVV',
                             type:"GET",
                             data:{
                                 VV:VV,
                                 V:V,
                                 STORE:STORE,
                                 PRODUCT:PRODUCT,  
                             },
                             dataType:"json",
                             beforeSend: function(){
                                 $('#loader').css("visibility", "visible");
                             },
   
                             success:function(data) {
                  $('#P_Code').val(data.code);
                  $('#AVQTY').val(data.qty);
                if(parseFloat(data.price) != 0){         
                  $('#PRICE').text(data.price);
                 $('#PriceHide').val(data.price);    
                        }         
                                     if(parseFloat(data.qty) == 0){
                            document.getElementById('StockNum').style.display='none';             
                            document.getElementById('In').style.display='none';             
                            document.getElementById('CART').style.display='none';             
                            document.getElementById('Out').style.display='block';       
                                     }else{
                                         
                                if(parseFloat(Quantity) > parseFloat(data.qty) || parseFloat(Quantity) == 0){
                                   
                         document.getElementById('StockNum').style.display='block';             
                            document.getElementById('In').style.display='none';             
                            document.getElementById('CART').style.display='none';             
                            document.getElementById('Out').style.display='block';                       
                                    
                                    
                                }else{
                              $('#StockNum').text(data.qty);
                            document.getElementById('StockNum').style.display='block';             
                            document.getElementById('In').style.display='block';             
                            document.getElementById('CART').style.display='block';             
                            document.getElementById('Out').style.display='none';                
                                    
                                }         
                                         
                                       
                                     }

                               
      
   
                             },
                             complete: function(){
                                 $('#loader').css("visibility", "hidden");
                             }
                         });
                
}


         
     }); 
       $('#MINUS').on('click', function(){
              var V = $('#V1').val();
   var VV = $('#V2').val();
   var STORE = $('#STORE').val();
   var PRODUCT = $('#PRODUCT').val();
   var Quantity = $('#qty').val();
  $.ajax({
                             url: '1/SiteProQtyVV',
                             type:"GET",
                             data:{
                                 VV:VV,
                                 V:V,
                                 STORE:STORE,
                                 PRODUCT:PRODUCT,  
                             },
                             dataType:"json",
                             beforeSend: function(){
                                 $('#loader').css("visibility", "visible");
                             },
   
                             success:function(data) {
                  $('#P_Code').val(data.code);
                  $('#AVQTY').val(data.qty);
               if(parseFloat(data.price) != 0){         
                  $('#PRICE').text(data.price);
                 $('#PriceHide').val(data.price);   
                        }       
                                     if(parseFloat(data.qty) == 0){
                            document.getElementById('StockNum').style.display='none';             
                            document.getElementById('In').style.display='none';             
                            document.getElementById('CART').style.display='none';             
                            document.getElementById('Out').style.display='block';       
                                     }else{
                                         
                                if(parseFloat(Quantity) > parseFloat(data.qty) || parseFloat(Quantity) == 0){
                                   
                         document.getElementById('StockNum').style.display='block';             
                            document.getElementById('In').style.display='none';             
                            document.getElementById('CART').style.display='none';             
                            document.getElementById('Out').style.display='block';                       
                                    
                                    
                                }else{
                              $('#StockNum').text(data.qty);
                            document.getElementById('StockNum').style.display='block';             
                            document.getElementById('In').style.display='block';             
                            document.getElementById('CART').style.display='block';             
                            document.getElementById('Out').style.display='none';                
                                    
                                }         
                                         
                                       
                                     }

                               
      
   
                             },
                             complete: function(){
                                 $('#loader').css("visibility", "hidden");
                             }
                         });
       });
             $('#PLUS').on('click', function(){
                    var V = $('#V1').val();
   var VV = $('#V2').val();
   var STORE = $('#STORE').val();
   var PRODUCT = $('#PRODUCT').val();
   var Quantity = $('#qty').val();
  $.ajax({
                             url: '1/SiteProQtyVV',
                             type:"GET",
                             data:{
                                 VV:VV,
                                 V:V,
                                 STORE:STORE,
                                 PRODUCT:PRODUCT,  
                             },
                             dataType:"json",
                             beforeSend: function(){
                                 $('#loader').css("visibility", "visible");
                             },
   
                             success:function(data) {
                  $('#P_Code').val(data.code);
                  $('#AVQTY').val(data.qty);
           if(parseFloat(data.price) != 0){         
                  $('#PRICE').text(data.price);
                $('#PriceHide').val(data.price);
                        }          
                                     if(parseFloat(data.qty) == 0){
                            document.getElementById('StockNum').style.display='none';             
                            document.getElementById('In').style.display='none';             
                            document.getElementById('CART').style.display='none';             
                            document.getElementById('Out').style.display='block';       
                                     }else{
                                         
                                if(parseFloat(Quantity) > parseFloat(data.qty) || parseFloat(Quantity) == 0){
                                   
                         document.getElementById('StockNum').style.display='block';             
                            document.getElementById('In').style.display='none';             
                            document.getElementById('CART').style.display='none';             
                            document.getElementById('Out').style.display='block';                       
                                    
                                    
                                }else{
                              $('#StockNum').text(data.qty);
                            document.getElementById('StockNum').style.display='block';             
                            document.getElementById('In').style.display='block';             
                            document.getElementById('CART').style.display='block';             
                            document.getElementById('Out').style.display='none';                
                                    
                                }         
                                         
                                       
                                     }

                               
      
   
                             },
                             complete: function(){
                                 $('#loader').css("visibility", "hidden");
                             }
                         });
             });
   $('#qty').on('click', function(){
          var V = $('#V1').val();
   var VV = $('#V2').val();
   var STORE = $('#STORE').val();
   var PRODUCT = $('#PRODUCT').val();
   var Quantity = $('#qty').val();
  $.ajax({
                             url: '1/SiteProQtyVV',
                             type:"GET",
                             data:{
                                 VV:VV,
                                 V:V,
                                 STORE:STORE,
                                 PRODUCT:PRODUCT,  
                             },
                             dataType:"json",
                             beforeSend: function(){
                                 $('#loader').css("visibility", "visible");
                             },
   
                             success:function(data) {
                  $('#P_Code').val(data.code);
                  $('#AVQTY').val(data.qty);
               if(parseFloat(data.price) != 0){         
                  $('#PRICE').text(data.price);
                 $('#PriceHide').val(data.price);   
                        }              
                                     if(parseFloat(data.qty) == 0){
                            document.getElementById('StockNum').style.display='none';             
                            document.getElementById('In').style.display='none';             
                            document.getElementById('CART').style.display='none';             
                            document.getElementById('Out').style.display='block';       
                                     }else{
                                         
                                if(parseFloat(Quantity) > parseFloat(data.qty) || parseFloat(Quantity) == 0){
                                   
                         document.getElementById('StockNum').style.display='block';             
                            document.getElementById('In').style.display='none';             
                            document.getElementById('CART').style.display='none';             
                            document.getElementById('Out').style.display='block';                       
                                    
                                    
                                }else{
                              $('#StockNum').text(data.qty);
                            document.getElementById('StockNum').style.display='block';             
                            document.getElementById('In').style.display='block';             
                            document.getElementById('CART').style.display='block';             
                            document.getElementById('Out').style.display='none';                
                                    
                                }         
                                         
                                       
                                     }

                               
      
   
                             },
                             complete: function(){
                                 $('#loader').css("visibility", "hidden");
                             }
                         });
   });
 $('#qty').on('keyup', function(){
        var V = $('#V1').val();
   var VV = $('#V2').val();
   var STORE = $('#STORE').val();
   var PRODUCT = $('#PRODUCT').val();
   var Quantity = $('#qty').val();
  $.ajax({
                             url: '1/SiteProQtyVV',
                             type:"GET",
                             data:{
                                 VV:VV,
                                 V:V,
                                 STORE:STORE,
                                 PRODUCT:PRODUCT,  
                             },
                             dataType:"json",
                             beforeSend: function(){
                                 $('#loader').css("visibility", "visible");
                             },
   
                             success:function(data) {
                  $('#P_Code').val(data.code);
                  $('#AVQTY').val(data.qty);
               if(parseFloat(data.price) != 0){         
                  $('#PRICE').text(data.price);
                 $('#PriceHide').val(data.price);   
                        }      
                                     if(parseFloat(data.qty) == 0){
                            document.getElementById('StockNum').style.display='none';             
                            document.getElementById('In').style.display='none';             
                            document.getElementById('CART').style.display='none';             
                            document.getElementById('Out').style.display='block';       
                                     }else{
                                         
                                if(parseFloat(Quantity) > parseFloat(data.qty) || parseFloat(Quantity) == 0){
                                   
                         document.getElementById('StockNum').style.display='block';             
                            document.getElementById('In').style.display='none';             
                            document.getElementById('CART').style.display='none';             
                            document.getElementById('Out').style.display='block';                       
                                    
                                    
                                }else{
                              $('#StockNum').text(data.qty);
                            document.getElementById('StockNum').style.display='block';             
                            document.getElementById('In').style.display='block';             
                            document.getElementById('CART').style.display='block';             
                            document.getElementById('Out').style.display='none';                
                                    
                                }         
                                         
                                       
                                     }

                               
      
   
                             },
                             complete: function(){
                                 $('#loader').css("visibility", "hidden");
                             }
                         });
 });
 $('#qty').on('keypress', function(){
        var V = $('#V1').val();
   var VV = $('#V2').val();
   var STORE = $('#STORE').val();
   var PRODUCT = $('#PRODUCT').val();
   var Quantity = $('#qty').val();
  $.ajax({
                             url: '1/SiteProQtyVV',
                             type:"GET",
                             data:{
                                 VV:VV,
                                 V:V,
                                 STORE:STORE,
                                 PRODUCT:PRODUCT,  
                             },
                             dataType:"json",
                             beforeSend: function(){
                                 $('#loader').css("visibility", "visible");
                             },
   
                             success:function(data) {
                  $('#P_Code').val(data.code);
                  $('#AVQTY').val(data.qty);
           if(parseFloat(data.price) != 0){         
                  $('#PRICE').text(data.price);
             $('#PriceHide').val(data.price);   
                        }        
                                     if(parseFloat(data.qty) == 0){
                            document.getElementById('StockNum').style.display='none';             
                            document.getElementById('In').style.display='none';             
                            document.getElementById('CART').style.display='none';             
                            document.getElementById('Out').style.display='block';       
                                     }else{
                                         
                                if(parseFloat(Quantity) > parseFloat(data.qty) || parseFloat(Quantity) == 0){
                                   
                         document.getElementById('StockNum').style.display='block';             
                            document.getElementById('In').style.display='none';             
                            document.getElementById('CART').style.display='none';             
                            document.getElementById('Out').style.display='block';                       
                                    
                                    
                                }else{
                              $('#StockNum').text(data.qty);
                            document.getElementById('StockNum').style.display='block';             
                            document.getElementById('In').style.display='block';             
                            document.getElementById('CART').style.display='block';             
                            document.getElementById('Out').style.display='none';                
                                    
                                }         
                                         
                                       
                                     }

                               
      
   
                             },
                             complete: function(){
                                 $('#loader').css("visibility", "hidden");
                             }
                         });
 });
</script>
<?php endif; ?>


<?php $__env->stopPush(); ?>
<?php echo $__env->make('site.index', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\ost-erp-online\resources\views/site/ProductDetails.blade.php ENDPATH**/ ?>