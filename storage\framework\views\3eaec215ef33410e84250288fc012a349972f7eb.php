<?php $__env->startSection('content'); ?>

  <title><?php echo e(trans('admin.About')); ?></title>
     <main id="js-page-content" role="main" class="page-content">
                    <ol class="breadcrumb page-breadcrumb">
                        <li class="breadcrumb-item"><a href="javascript:void(0);"><?php echo e(trans('admin.Website')); ?></a></li>
                      
                        <li class="breadcrumb-item active"> <?php echo e(trans('admin.About')); ?>   </li>
                        <li class="position-absolute pos-top pos-right d-none d-sm-block"><span
                                class="js-get-date"></span></li>
                    </ol>
                                          <style>
      
    
       .Hei{
          background:#886ab5;
           color:WHITE;
          
           padding:10px;
        margin-bottom:20px;
        box-shadow: 0 2px 6px 0 rgba(136, 106, 181, 0.5);
       }
       .wei{
                background:#886ab5;
           color:WHITE;
       
           padding:10px;
           margin-bottom:20px;
           box-shadow: 0 2px 6px 0 rgba(136, 106, 181, 0.5);
       }
       .BlogsH{
           display:flex;
           text-align:center;
               flex-direction: column;
       }
   </style>



               <div class="Sizes">
                          
       <div class="container row">
       <div class="col-lg-12 text-center"><h2>Size Of Picture:</h2></div>
            <div  class="col-lg-6 text-center Hei"><h2 class="BlogsH">About us</h2>width:636px^Height:393px</div>
       

           
           
   </div> 
   </div>
                 
                    <!-- data entry -->
                    <div class="row">
                        <div class="col-lg-12">
                            <div id="panel-2" class="panel">
                                <div class="panel-hdr">

                                </div>
                                <div class="panel-container show">
                                  <span id="ex"> <?php echo $__env->make('admin.layouts.messages', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?></span>     
                                    <div class="panel-content">
                                        <ul class="nav nav-tabs" role="tablist">
                                            <li class="nav-item">
                                                <a class="nav-link active" data-toggle="tab" href="#tab_borders_icons-8" role="tab">   <?php echo e(trans('admin.About')); ?>  </a>
                                            </li>
                                        </ul>
                                        <div class="tab-content border border-top-0 p-3">
                           <div class="tab-pane fade show active" id="tab_borders_icons-8" role="tabpanel">
                                                                          
     <form action="<?php echo e(url('UpdateAbout/'.$item->id)); ?>" method="post" enctype="multipart/form-data" class="form-row">
                                  <?php echo csrf_field(); ?>

                   <?php echo view('honeypot::honeypotFormFields'); ?>
        <input type="hidden" name="Images" value="<?php echo e($item->Image); ?>"> 
        <input type="hidden" name="Images_2" value="<?php echo e($item->Image_2); ?>"> 
        <input type="hidden" name="Images_3" value="<?php echo e($item->Image_3); ?>"> 

                                                    <div class="col-md-12">
                                                        <div class="data-def">
                                                            <div class="form-row">
                               
                                          <div class="form-group col-md-12">    
 <label class="form-label" for=""> <?php echo e(trans('admin.Image')); ?> </label>       
                            <input type="file" name="Image">                
                                              <img src="<?php echo e(URL::to($item->Image)); ?>" class="img-fluid">
                                                                </div>  
                                                                
                                                            <div class="form-group col-md-4" style="display:none">    
 <label class="form-label" for=""> <?php echo e(trans('admin.Image_2')); ?> </label>       
                            <input type="file" name="Image_2">                
                                              <img src="<?php echo e(URL::to($item->Image_2)); ?>" class="img-fluid">
                                                                </div>                             
                                           
                        <div class="form-group col-md-4" style="display:none">    
 <label class="form-label" for=""> <?php echo e(trans('admin.Image_3')); ?> </label>       
                            <input type="file" name="Image_3">                
                                              <img src="<?php echo e(URL::to($item->Image_3)); ?>" class="img-fluid">
                                                                </div>   
                  
                                                                
                                                     <div class="form-group col-md-6">
                                          <label class="form-label" for=""> <?php echo e(trans('admin.Arabic_Title')); ?>   </label>
                              <input type="text" class="form-control" name="Arabic_Title" value="<?php echo e($item->Arabic_Title); ?>" required>
                                                                </div>                            
                                  
                                          <div class="form-group col-md-6">
                                          <label class="form-label" for=""> <?php echo e(trans('admin.English_Title')); ?>   </label>
              <input type="text" class="form-control" name="English_Title" value="<?php echo e($item->English_Title); ?>" required>
                                                                </div>                                         
                                                                
                                                                
                                  <div class="form-group col-md-6" style="display: none">
                                       <label class="form-label" for=""> <?php echo e(trans('admin.Arabic_Desc')); ?>   </label>
                                       <textarea class="js-summernote" id="saveToLocal" name="">
                                       <?php echo e($item->Arabic_Desc); ?>          
                                       </textarea>
                                    </div>                            
                                                                
                                                 <div class="form-group col-md-6">
                                       <label class="form-label" for=""> <?php echo e(trans('admin.Arabic_Desc')); ?>   </label>
                                       <textarea class="js-summernote" id="saveToLocal" name="Arabic_Desc">
                                       <?php echo e($item->Arabic_Desc); ?>          
                                       </textarea>
                                    </div>                      
                                                                
                     
                                                                
                                                                
                                                    <div class="form-group col-md-6" style="display: none">
                                       <label class="form-label" for=""> <?php echo e(trans('admin.English_Desc')); ?>   </label>
                                       <textarea class="js-summernote" id="saveToLocal" name="">
                                       <?php echo e($item->English_Desc); ?>          
                                       </textarea>
                                    </div>                            
                                                                
                                                 <div class="form-group col-md-6">
                                       <label class="form-label" for=""> <?php echo e(trans('admin.English_Desc')); ?>   </label>
                                       <textarea class="js-summernote" id="saveToLocal" name="English_Desc">
                                       <?php echo e($item->English_Desc); ?>          
                                       </textarea>
                                    </div>                                        
                                                                
                                                       <hr>    
                                            
       
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-12">
                                <button type="submit" class="btn btn-primary mt-2"><?php echo e(trans('admin.Save')); ?></button>
                                                    </div>
                                                </form>
 
                                            </div>
                                        </div>
                                      
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                 
                </main>
 
<?php $__env->stopSection(); ?>

<?php $__env->startPush('js'); ?>
  <link rel="stylesheet" media="screen, print" href="<?php echo e(asset('Admin/css/datagrid/datatables/datatables.bundle.css')); ?>">
    <link rel="stylesheet" media="screen, print" href="<?php echo e(asset('Admin/css/formplugins/summernote/summernote.css')); ?>">
    <link rel="stylesheet" media="screen, print" href="<?php echo e(asset('Admin/css/formplugins/select2/select2.bundle.css')); ?>">

 <script src="<?php echo e(asset('Admin/js/datagrid/datatables/datatables.bundle.js')); ?>"></script>
    <script src="<?php echo e(asset('Admin/js/datagrid/datatables/datatables.export.js')); ?>"></script>
    <script src="<?php echo e(asset('Admin/js/formplugins/summernote/summernote.js')); ?>"></script>
    <script src="<?php echo e(asset('Admin/js/formplugins/select2/select2.bundle.js')); ?>"></script>
    <script>
        //_fnFeatureHtmlLength();
        $(document).ready(function () {
            // Setup - add a text input to each footer cell
            $('#dt-basic-example thead tr').clone(true).appendTo('#dt-basic-example thead');
            $('#dt-basic-example thead tr:eq(1) th').each(function (i) {
                var title = $(this).text();
                $(this).html('<input type="text" class="form-control form-control-sm" placeholder="Search ' + title + '" />');

                $('input', this).on('keyup change', function () {
                    if (table.column(i).search() !== this.value) {
                        table
                            .column(i)
                            .search(this.value)
                            .draw();
                    }
                });
            });
            var table = $('#dt-basic-example').DataTable(
                {
                    responsive: true,
                    orderCellsTop: true,
                    fixedHeader: true,
                    lengthChange: true,

                    dom: "<'row mb-3'<'col-sm-12 col-md-3 d-flex align-items-center justify-content-start'f><'col-sm-12 col-md-9 d-flex align-items-center justify-content-end'B>>" +
                        "<'row'<'col-sm-12'tr>>" +
                        "<'row'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7'p>>",

                    buttons: [
                        {
                            extend: 'pageLength',
                            className: 'btn-outline-default'
                        },
                        {
                            extend: 'colvis',
                            text: 'Column Visibility',
                            titleAttr: 'Col visibility',
                            className: 'btn-outline-default'
                        },
                        {
                            extend: 'pdfHtml5',
                            text: 'PDF',
                            titleAttr: 'Generate PDF',
                            className: 'btn-outline-danger btn-sm mr-1'
                        },
                        {
                            extend: 'excelHtml5',
                            text: 'Excel',
                            titleAttr: 'Generate Excel',
                            className: 'btn-outline-success btn-sm mr-1'
                        },
                        {
                            extend: 'csvHtml5',
                            text: 'CSV',
                            titleAttr: 'Generate CSV',
                            className: 'btn-outline-primary btn-sm mr-1'
                        },
                        {
                            extend: 'copyHtml5',
                            text: 'Copy',
                            titleAttr: 'Copy to clipboard',
                            className: 'btn-outline-primary btn-sm mr-1'
                        },
                        {
                            extend: 'print',
                            text: 'Print',
                            titleAttr: 'Print Table',
                            className: 'btn-outline-primary btn-sm'
                        }
                    ],
                });
            $('.js-thead-colors a').on('click', function () {
                var theadColor = $(this).attr("data-bg");
                console.log(theadColor);
                $('#dt-basic-example thead').removeClassPrefix('bg-').addClass(theadColor);
            });

            $('.js-tbody-colors a').on('click', function () {
                var theadColor = $(this).attr("data-bg");
                console.log(theadColor);
                $('#dt-basic-example').removeClassPrefix('bg-').addClass(theadColor);
            });

        });

    </script>
<script src="js/formplugins/summernote/summernote.js"></script>
<script>
   var autoSave = $('#autoSave');
   var interval;
   var timer = function()
   {
       interval = setInterval(function()
       {
           //start slide...
           if (autoSave.prop('checked'))
               saveToLocal();
   
           clearInterval(interval);
       }, 3000);
   };
   
   //save
   var saveToLocal = function()
   {
       localStorage.setItem('summernoteData', $('#saveToLocal').summernote("code"));
       console.log("saved");
   }
   
   //delete 
   var removeFromLocal = function()
   {
       localStorage.removeItem("summernoteData");
       $('#saveToLocal').summernote('reset');
   }
   
   $(document).ready(function()
   {
       //init default
       $('.js-summernote').summernote(
       {
           height: 200,
           tabsize: 2,
           placeholder: "Type here...",
           dialogsFade: true,
           toolbar: [
               ['style', ['style']],
               ['font', ['strikethrough', 'superscript', 'subscript']],
               ['font', ['bold', 'italic', 'underline', 'clear']],
               ['fontsize', ['fontsize']],
               ['fontname', ['fontname']],
               ['color', ['color']],
               ['para', ['ul', 'ol', 'paragraph']],
               ['height', ['height']]
               ['table', ['table']],
               ['insert', ['link', 'picture', 'video']],
               ['view', ['fullscreen', 'codeview', 'help']]
           ],
           callbacks:
           {
               //restore from localStorage
               onInit: function(e)
               {
                   $('.js-summernote').summernote("code", localStorage.getItem("summernoteData"));
               },
               onChange: function(contents, $editable)
               {
                   clearInterval(interval);
                   timer();
               }
           }
       });
   
       //load emojis
       $.ajax(
       {
           url: 'https://api.github.com/emojis',
           async: false
       }).then(function(data)
       {
           window.emojis = Object.keys(data);
           window.emojiUrls = data;
       });
   
       //init emoji example
       $(".js-hint2emoji").summernote(
       {
           height: 100,
           toolbar: false,
           placeholder: 'type starting with : and any alphabet',
           hint:
           {
               match: /:([\-+\w]+)$/,
               search: function(keyword, callback)
               {
                   callback($.grep(emojis, function(item)
                   {
                       return item.indexOf(keyword) === 0;
                   }));
               },
               template: function(item)
               {
                   var content = emojiUrls[item];
                   return '<img src="' + content + '" width="20" /> :' + item + ':';
               },
               content: function(item)
               {
                   var url = emojiUrls[item];
                   if (url)
                   {
                       return $('<img />').attr('src', url).css('width', 20)[0];
                   }
                   return '';
               }
           }
       });
   
       //init mentions example
       $(".js-hint2mention").summernote(
       {
           height: 100,
           toolbar: false,
           placeholder: "type starting with @",
           hint:
           {
               mentions: ['jayden', 'sam', 'alvin', 'david'],
               match: /\B@(\w*)$/,
               search: function(keyword, callback)
               {
                   callback($.grep(this.mentions, function(item)
                   {
                       return item.indexOf(keyword) == 0;
                   }));
               },
               content: function(item)
               {
                   return '@' + item;
               }
           }
       });
   
   });
   
</script>
<?php $__env->stopPush(); ?>


<?php echo $__env->make('admin.index', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\ost-erp-online\resources\views/admin/Website/About.blade.php ENDPATH**/ ?>