/*
===========================================
Modern Tech/SaaS Style Framework
===========================================
This file contains modern styling for all website elements
to create a consistent tech/SaaS company appearance
*/

/* CSS Variables for Modern Tech Colors - Matching Navbar */
:root {
    /* Primary gradients matching navbar */
    --primary-gradient: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
    --secondary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    --warning-gradient: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    --danger-gradient: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
    --accent-gradient: linear-gradient(135deg, #64b5f6 0%, #42a5f5 100%);

    /* Main colors matching navbar design */
    --primary-color: #1e3c72;
    --primary-light: #2a5298;
    --secondary-color: #667eea;
    --secondary-light: #764ba2;
    --accent-color: #64b5f6;
    --accent-hover: #42a5f5;

    /* Text colors */
    --text-dark: #2c3e50;
    --text-medium: #34495e;
    --text-light: #7f8c8d;
    --text-white: #ffffff;
    --text-muted: rgba(255,255,255,0.8);

    /* Background colors */
    --bg-primary: #1e3c72;
    --bg-secondary: #2a5298;
    --bg-light: #f8f9fa;
    --bg-white: #ffffff;
    --bg-gray: #e9ecef;
    --bg-dark: #2c3e50;

    /* Border and shadow colors */
    --border-light: #e9ecef;
    --border-primary: #1e3c72;
    --shadow-light: 0 2px 10px rgba(30, 60, 114, 0.08);
    --shadow-medium: 0 4px 20px rgba(30, 60, 114, 0.12);
    --shadow-heavy: 0 10px 30px rgba(30, 60, 114, 0.15);
    --shadow-colored: 0 4px 15px rgba(102, 126, 234, 0.3);

    /* Border radius */
    --border-radius: 15px;
    --border-radius-small: 8px;
    --border-radius-large: 25px;
    --transition: all 0.3s ease;
}

/* Modern Typography */
body {
    font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
    color: var(--text-dark) !important;
    line-height: 1.6 !important;
}

h1, h2, h3, h4, h5, h6 {
    font-weight: 700 !important;
    color: var(--text-dark) !important;
    margin-bottom: 1rem !important;
}

h1 { font-size: 2.5rem !important; }
h2 { font-size: 2rem !important; }
h3 { font-size: 1.75rem !important; }
h4 { font-size: 1.5rem !important; }
h5 { font-size: 1.25rem !important; }
h6 { font-size: 1rem !important; }

p {
    color: var(--text-light) !important;
    font-weight: 400 !important;
    margin-bottom: 1rem !important;
}

/* Modern Buttons - Matching Navbar Colors */
.default-btn, .btn, button[type="submit"], input[type="submit"] {
    background: var(--primary-gradient) !important;
    border: none !important;
    border-radius: var(--border-radius-large) !important;
    padding: 12px 30px !important;
    font-weight: 600 !important;
    font-size: 14px !important;
    color: var(--text-white) !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
    transition: var(--transition) !important;
    box-shadow: var(--shadow-colored) !important;
    position: relative !important;
    overflow: hidden !important;
    text-decoration: none !important;
    display: inline-flex !important;
    align-items: center !important;
    gap: 8px !important;
}

.default-btn:hover, .btn:hover, button[type="submit"]:hover, input[type="submit"]:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4) !important;
    background: var(--secondary-gradient) !important;
    color: var(--text-white) !important;
    text-decoration: none !important;
}

.default-btn i, .btn i {
    font-size: 16px !important;
}

/* Secondary Button Style */
.btn-secondary, .default-btn.secondary {
    background: var(--bg-white) !important;
    color: var(--primary-color) !important;
    border: 2px solid var(--primary-color) !important;
    box-shadow: var(--shadow-light) !important;
}

.btn-secondary:hover, .default-btn.secondary:hover {
    background: var(--primary-gradient) !important;
    color: var(--text-white) !important;
    border-color: transparent !important;
    box-shadow: var(--shadow-colored) !important;
}

/* Accent Button Style */
.btn-accent, .default-btn.accent {
    background: var(--accent-gradient) !important;
    color: var(--text-white) !important;
    border: none !important;
}

.btn-accent:hover, .default-btn.accent:hover {
    background: linear-gradient(135deg, #42a5f5 0%, #1e88e5 100%) !important;
    box-shadow: 0 4px 15px rgba(100, 181, 246, 0.4) !important;
}

/* Modern Cards */
.single-overview, .single-arrivals-products, .single-bestsellers-products,
.single-special-products, .single-offer-products, .single-hot-products,
.single-blog, .single-partner, .single-featured, .single-testimonial {
    background: var(--bg-white) !important;
    border-radius: var(--border-radius) !important;
    box-shadow: var(--shadow-light) !important;
    padding: 30px !important;
    margin-bottom: 30px !important;
    transition: var(--transition) !important;
    border: 1px solid var(--border-light) !important;
}

.single-overview:hover, .single-arrivals-products:hover, .single-bestsellers-products:hover,
.single-special-products:hover, .single-offer-products:hover, .single-hot-products:hover,
.single-blog:hover, .single-partner:hover, .single-featured:hover, .single-testimonial:hover {
    transform: translateY(-5px) !important;
    box-shadow: var(--shadow-medium) !important;
}

/* Modern Product Cards */
.products-box {
    background: var(--bg-white) !important;
    border-radius: var(--border-radius) !important;
    box-shadow: var(--shadow-light) !important;
    overflow: hidden !important;
    transition: var(--transition) !important;
    border: 1px solid var(--border-light) !important;
}

.products-box:hover {
    transform: translateY(-5px) !important;
    box-shadow: var(--shadow-medium) !important;
}

.products-image {
    position: relative !important;
    overflow: hidden !important;
    border-radius: var(--border-radius) var(--border-radius) 0 0 !important;
}

.products-image img {
    transition: var(--transition) !important;
    border-radius: var(--border-radius) var(--border-radius) 0 0 !important;
}

.products-image:hover img {
    transform: scale(1.05) !important;
}

.products-content {
    padding: 20px !important;
}

.products-content h3 {
    font-size: 18px !important;
    font-weight: 600 !important;
    margin-bottom: 10px !important;
    color: var(--text-dark) !important;
}

.products-content .price {
    font-size: 20px !important;
    font-weight: 700 !important;
    color: var(--primary-color) !important;
    margin-bottom: 15px !important;
}

/* Modern Forms - Matching Navbar Colors */
.form-control, input[type="text"], input[type="email"], input[type="password"],
input[type="number"], textarea, select {
    border: 2px solid var(--border-light) !important;
    border-radius: var(--border-radius-small) !important;
    padding: 12px 15px !important;
    font-size: 14px !important;
    transition: var(--transition) !important;
    background: var(--bg-white) !important;
    color: var(--text-dark) !important;
}

.form-control:focus, input:focus, textarea:focus, select:focus {
    border-color: var(--primary-color) !important;
    box-shadow: 0 0 0 3px rgba(30, 60, 114, 0.1) !important;
    outline: none !important;
    background: rgba(30, 60, 114, 0.02) !important;
}

/* Modern Sections */
.section-title {
    text-align: center !important;
    margin-bottom: 60px !important;
}

.section-title h2 {
    font-size: 2.5rem !important;
    font-weight: 700 !important;
    color: var(--text-dark) !important;
    margin-bottom: 15px !important;
    position: relative !important;
}

.section-title h2::after {
    content: '' !important;
    position: absolute !important;
    bottom: -10px !important;
    left: 50% !important;
    transform: translateX(-50%) !important;
    width: 60px !important;
    height: 4px !important;
    background: var(--primary-gradient) !important;
    border-radius: 2px !important;
    box-shadow: 0 2px 8px rgba(30, 60, 114, 0.3) !important;
}

.section-title p {
    font-size: 16px !important;
    color: var(--text-light) !important;
    max-width: 600px !important;
    margin: 0 auto !important;
}

/* Modern Spacing */
.pt-100 { padding-top: 100px !important; }
.pb-100 { padding-bottom: 100px !important; }
.pt-70 { padding-top: 70px !important; }
.pb-70 { padding-bottom: 70px !important; }
.pt-50 { padding-top: 50px !important; }
.pb-50 { padding-bottom: 50px !important; }

/* Modern Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out !important;
}

/* Modern Responsive */
@media (max-width: 991px) {
    h1 { font-size: 2rem !important; }
    h2 { font-size: 1.75rem !important; }
    h3 { font-size: 1.5rem !important; }
    
    .section-title h2 {
        font-size: 2rem !important;
    }
    
    .single-overview, .single-arrivals-products, .single-bestsellers-products,
    .single-special-products, .single-offer-products, .single-hot-products {
        padding: 20px !important;
    }
}

@media (max-width: 767px) {
    h1 { font-size: 1.75rem !important; }
    h2 { font-size: 1.5rem !important; }
    
    .default-btn, .btn {
        padding: 10px 20px !important;
        font-size: 13px !important;
    }
    
    .section-title {
        margin-bottom: 40px !important;
    }
    
    .section-title h2 {
        font-size: 1.75rem !important;
    }
}

/* Modern Slider Styles - Matching Navbar Colors */
.main-slider-item {
    background: var(--primary-gradient) !important;
    position: relative !important;
    overflow: hidden !important;
}

.main-slider-item::before {
    content: '' !important;
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    background: linear-gradient(135deg, rgba(30, 60, 114, 0.8) 0%, rgba(42, 82, 152, 0.6) 100%) !important;
    z-index: 1 !important;
}

.main-slider-content {
    position: relative !important;
    z-index: 2 !important;
}

.main-slider-content h1 {
    font-size: 3rem !important;
    font-weight: 800 !important;
    color: var(--text-white) !important;
    margin-bottom: 20px !important;
    line-height: 1.2 !important;
}

.main-slider-content p {
    font-size: 18px !important;
    color: rgba(255,255,255,0.9) !important;
    margin-bottom: 30px !important;
    line-height: 1.6 !important;
}

.main-slider-image {
    position: relative !important;
    z-index: 2 !important;
}

.main-slider-image img {
    border-radius: var(--border-radius) !important;
    box-shadow: var(--shadow-heavy) !important;
}

/* Modern Overview Section */
.overview-area {
    background: var(--bg-light) !important;
}

.overview-content h3 {
    font-size: 24px !important;
    font-weight: 700 !important;
    color: var(--text-dark) !important;
    margin-bottom: 15px !important;
}

.overview-content p {
    color: var(--text-light) !important;
    margin-bottom: 20px !important;
}

.overview-image img {
    border-radius: var(--border-radius) !important;
    box-shadow: var(--shadow-light) !important;
}

/* Modern Product Sections */
.arrivals-products-area, .bestsellers-area, .special-products-area,
.offer-products-area, .hot-products-area {
    background: var(--bg-white) !important;
}

.arrivals-products-area:nth-child(even), .bestsellers-area:nth-child(even),
.special-products-area:nth-child(even), .offer-products-area:nth-child(even),
.hot-products-area:nth-child(even) {
    background: var(--bg-light) !important;
}

/* Modern Product Actions */
.products-box .products-image ul {
    position: absolute !important;
    top: 15px !important;
    right: 15px !important;
    list-style: none !important;
    padding: 0 !important;
    margin: 0 !important;
    display: flex !important;
    flex-direction: column !important;
    gap: 10px !important;
}

.products-box .products-image ul li {
    background: var(--bg-white) !important;
    border-radius: 50% !important;
    width: 40px !important;
    height: 40px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    box-shadow: var(--shadow-light) !important;
    transition: var(--transition) !important;
}

.products-box .products-image ul li:hover {
    background: var(--primary-gradient) !important;
    transform: scale(1.1) !important;
    box-shadow: var(--shadow-colored) !important;
}

.products-box .products-image ul li a {
    color: var(--text-dark) !important;
    font-size: 16px !important;
    text-decoration: none !important;
    transition: var(--transition) !important;
}

.products-box .products-image ul li:hover a {
    color: var(--text-white) !important;
}

/* Modern Blog Section */
.blog-area {
    background: var(--bg-light) !important;
}

.single-blog {
    border-radius: var(--border-radius) !important;
    overflow: hidden !important;
}

.blog-image {
    position: relative !important;
    overflow: hidden !important;
}

.blog-image img {
    transition: var(--transition) !important;
    width: 100% !important;
    height: 250px !important;
    object-fit: cover !important;
}

.single-blog:hover .blog-image img {
    transform: scale(1.05) !important;
}

.blog-content {
    padding: 25px !important;
}

.blog-content h3 {
    font-size: 20px !important;
    font-weight: 600 !important;
    margin-bottom: 15px !important;
}

.blog-content h3 a {
    color: var(--text-dark) !important;
    text-decoration: none !important;
    transition: var(--transition) !important;
}

.blog-content h3 a:hover {
    color: var(--primary-color) !important;
}

.blog-content p {
    color: var(--text-light) !important;
    margin-bottom: 20px !important;
    line-height: 1.6 !important;
}

.blog-content .blog-btn a {
    color: var(--primary-color) !important;
    font-weight: 600 !important;
    text-decoration: none !important;
    display: inline-flex !important;
    align-items: center !important;
    gap: 5px !important;
    transition: var(--transition) !important;
    padding: 8px 16px !important;
    border-radius: var(--border-radius-small) !important;
    background: rgba(30, 60, 114, 0.1) !important;
}

.blog-content .blog-btn a:hover {
    color: var(--text-white) !important;
    background: var(--primary-gradient) !important;
    transform: translateX(5px) !important;
    box-shadow: var(--shadow-colored) !important;
}

/* Modern Footer */
.footer-area {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%) !important;
    color: var(--text-white) !important;
    position: relative !important;
}

.footer-area::before {
    content: '' !important;
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    height: 1px !important;
    background: var(--primary-gradient) !important;
}

.single-footer-widget h2 {
    color: var(--text-white) !important;
    font-size: 20px !important;
    font-weight: 600 !important;
    margin-bottom: 25px !important;
    position: relative !important;
}

.single-footer-widget h2::after {
    content: '' !important;
    position: absolute !important;
    bottom: -8px !important;
    left: 0 !important;
    width: 40px !important;
    height: 3px !important;
    background: var(--primary-gradient) !important;
    border-radius: 2px !important;
}

.footer-contact-info li {
    margin-bottom: 15px !important;
    display: flex !important;
    align-items: flex-start !important;
    gap: 10px !important;
}

.footer-contact-info li span {
    color: var(--primary-color) !important;
    font-weight: 600 !important;
    min-width: 80px !important;
}

.footer-contact-info li a {
    color: rgba(255,255,255,0.8) !important;
    text-decoration: none !important;
    transition: var(--transition) !important;
}

.footer-contact-info li a:hover {
    color: var(--text-white) !important;
}

.footer-social {
    display: flex !important;
    gap: 15px !important;
    margin-top: 25px !important;
}

.footer-social li a {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    width: 45px !important;
    height: 45px !important;
    background: rgba(255,255,255,0.1) !important;
    border-radius: 50% !important;
    color: var(--text-white) !important;
    font-size: 18px !important;
    transition: var(--transition) !important;
    text-decoration: none !important;
}

.footer-social li a:hover {
    background: var(--primary-color) !important;
    transform: translateY(-3px) !important;
    box-shadow: var(--shadow-light) !important;
}

.footer-widget-list li {
    margin-bottom: 12px !important;
}

.footer-widget-list li a {
    color: rgba(255,255,255,0.8) !important;
    text-decoration: none !important;
    transition: var(--transition) !important;
    display: flex !important;
    align-items: center !important;
    gap: 8px !important;
}

.footer-widget-list li a:hover {
    color: var(--text-white) !important;
    padding-left: 10px !important;
}

.footer-widget-list li a::before {
    content: '→' !important;
    color: var(--primary-color) !important;
    font-weight: bold !important;
}

/* Modern Copyright */
.copyright-area {
    background: rgba(0,0,0,0.2) !important;
    border-top: 1px solid rgba(255,255,255,0.1) !important;
    padding: 20px 0 !important;
}

.copyright-area p {
    color: rgba(255,255,255,0.8) !important;
    margin: 0 !important;
    text-align: center !important;
}

/* Modern Pagination */
.pagination-area {
    margin-top: 50px !important;
    text-align: center !important;
}

.page-numbers {
    display: inline-flex !important;
    gap: 10px !important;
    list-style: none !important;
    padding: 0 !important;
    margin: 0 !important;
}

.page-numbers li a, .page-numbers li span {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    width: 45px !important;
    height: 45px !important;
    border-radius: 50% !important;
    background: var(--bg-white) !important;
    color: var(--text-dark) !important;
    text-decoration: none !important;
    font-weight: 600 !important;
    transition: var(--transition) !important;
    border: 2px solid var(--border-light) !important;
}

.page-numbers li a:hover, .page-numbers li .current {
    background: var(--primary-gradient) !important;
    color: var(--text-white) !important;
    border-color: var(--primary-color) !important;
    transform: translateY(-2px) !important;
    box-shadow: var(--shadow-colored) !important;
}

/* Modern Modal Styles */
.modal-content {
    border-radius: var(--border-radius) !important;
    border: none !important;
    box-shadow: var(--shadow-heavy) !important;
}

.modal-header {
    background: var(--primary-gradient) !important;
    color: var(--text-white) !important;
    border-radius: var(--border-radius) var(--border-radius) 0 0 !important;
    border-bottom: none !important;
    box-shadow: 0 2px 10px rgba(30, 60, 114, 0.2) !important;
}

.modal-header .modal-title {
    color: var(--text-white) !important;
    font-weight: 600 !important;
}

.modal-header .close {
    color: var(--text-white) !important;
    opacity: 0.8 !important;
}

.modal-header .close:hover {
    opacity: 1 !important;
}

.modal-body {
    padding: 30px !important;
}

.modal-footer {
    border-top: 1px solid var(--border-light) !important;
    padding: 20px 30px !important;
}

/* Modern Map Container */
.modern-map-container {
    border-radius: var(--border-radius) !important;
    overflow: hidden !important;
    box-shadow: var(--shadow-light) !important;
    border: 1px solid var(--border-light) !important;
    height: 250px !important;
}

.modern-map-container iframe {
    width: 100% !important;
    height: 100% !important;
    border: none !important;
    filter: grayscale(20%) !important;
    transition: var(--transition) !important;
}

.modern-map-container:hover iframe {
    filter: grayscale(0%) !important;
}

/* Modern Breadcrumb - Matching Navbar Colors */
.page-title-area {
    background: var(--primary-gradient) !important;
    position: relative !important;
    padding: 100px 0 !important;
}

.page-title-area::before {
    content: '' !important;
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    background: linear-gradient(135deg, rgba(30, 60, 114, 0.1) 0%, rgba(42, 82, 152, 0.2) 100%) !important;
}

.page-title-content {
    position: relative !important;
    z-index: 2 !important;
    text-align: center !important;
}

.page-title-content h2 {
    color: var(--text-white) !important;
    font-size: 2.5rem !important;
    font-weight: 700 !important;
    margin-bottom: 15px !important;
}

.page-title-content ul {
    list-style: none !important;
    padding: 0 !important;
    margin: 0 !important;
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
    gap: 10px !important;
}

.page-title-content ul li {
    color: rgba(255,255,255,0.8) !important;
    font-weight: 500 !important;
}

.page-title-content ul li a {
    color: var(--text-white) !important;
    text-decoration: none !important;
    transition: var(--transition) !important;
}

.page-title-content ul li a:hover {
    color: rgba(255,255,255,0.8) !important;
}

/* Modern Tables */
.table {
    background: var(--bg-white) !important;
    border-radius: var(--border-radius) !important;
    overflow: hidden !important;
    box-shadow: var(--shadow-light) !important;
    border: 1px solid var(--border-light) !important;
}

.table thead th {
    background: var(--primary-gradient) !important;
    color: var(--text-white) !important;
    font-weight: 600 !important;
    border: none !important;
    padding: 15px !important;
    box-shadow: 0 2px 8px rgba(30, 60, 114, 0.2) !important;
}

.table tbody td {
    padding: 15px !important;
    border-color: var(--border-light) !important;
    color: var(--text-dark) !important;
}

.table tbody tr:hover {
    background: var(--bg-light) !important;
}

/* Modern Alerts */
.alert {
    border-radius: var(--border-radius) !important;
    border: none !important;
    padding: 15px 20px !important;
    font-weight: 500 !important;
    box-shadow: var(--shadow-light) !important;
}

.alert-success {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%) !important;
    color: #155724 !important;
}

.alert-danger {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%) !important;
    color: #721c24 !important;
}

.alert-warning {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%) !important;
    color: #856404 !important;
}

.alert-info {
    background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%) !important;
    color: #0c5460 !important;
}

/* Modern Badge */
.badge {
    border-radius: var(--border-radius-small) !important;
    font-weight: 600 !important;
    font-size: 11px !important;
    padding: 5px 10px !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
}

.badge-primary {
    background: var(--primary-gradient) !important;
    color: var(--text-white) !important;
    box-shadow: 0 2px 8px rgba(30, 60, 114, 0.3) !important;
}

.badge-secondary {
    background: var(--secondary-gradient) !important;
    color: var(--text-white) !important;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3) !important;
}

.badge-accent {
    background: var(--accent-gradient) !important;
    color: var(--text-white) !important;
    box-shadow: 0 2px 8px rgba(100, 181, 246, 0.3) !important;
}

.badge-success {
    background: var(--success-gradient) !important;
    color: var(--text-white) !important;
}

.badge-warning {
    background: var(--warning-gradient) !important;
    color: var(--text-white) !important;
}

.badge-danger {
    background: var(--danger-gradient) !important;
    color: var(--text-white) !important;
}

/* Modern Loading States */
.loading {
    position: relative !important;
    overflow: hidden !important;
}

.loading::after {
    content: '' !important;
    position: absolute !important;
    top: 0 !important;
    left: -100% !important;
    width: 100% !important;
    height: 100% !important;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent) !important;
    animation: loading 1.5s infinite !important;
}

@keyframes loading {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* Modern Scroll to Top - Matching Navbar Colors */
.go-top {
    position: fixed !important;
    bottom: 30px !important;
    right: 30px !important;
    width: 50px !important;
    height: 50px !important;
    background: var(--primary-gradient) !important;
    color: var(--text-white) !important;
    border-radius: 50% !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    font-size: 20px !important;
    box-shadow: var(--shadow-colored) !important;
    transition: var(--transition) !important;
    z-index: 999 !important;
    text-decoration: none !important;
    border: 2px solid rgba(255,255,255,0.2) !important;
}

.go-top:hover {
    transform: translateY(-3px) scale(1.1) !important;
    box-shadow: 0 8px 25px rgba(30, 60, 114, 0.4) !important;
    background: var(--secondary-gradient) !important;
    color: var(--text-white) !important;
    text-decoration: none !important;
    border-color: rgba(255,255,255,0.3) !important;
}

/* Modern Home Page Styles - Matching Navbar */

/* Modern Hero Slider */
.modern-hero-slider {
    position: relative !important;
}

.modern-slide-item {
    position: relative !important;
    min-height: 600px !important;
    display: flex !important;
    align-items: center !important;
    overflow: hidden !important;
}

.modern-slide-overlay {
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    background: transparent !important;
    z-index: 1 !important;
}

.modern-slide-content {
    position: relative !important;
    z-index: 2 !important;
    padding: 60px 0 !important;
    text-align: left !important;
}

/* Arabic RTL Support */
[dir="rtl"] .modern-slide-content,
html[lang="ar"] .modern-slide-content {
    text-align: right !important;
}

.modern-slide-title {
    font-size: 3.5rem !important;
    font-weight: 800 !important;
    color: rgba(255,255,255,0.9) !important;
    margin-bottom: 25px !important;
    line-height: 1.2 !important;
    text-shadow: none !important;
}

.modern-slide-description {
    font-size: 20px !important;
    color: rgba(255,255,255,0.8) !important;
    margin-bottom: 40px !important;
    line-height: 1.6 !important;
    font-weight: 400 !important;
    text-shadow: none !important;
}

.modern-slide-actions {
    display: flex !important;
    gap: 20px !important;
    flex-wrap: wrap !important;
    justify-content: flex-start !important;
}

/* Arabic RTL Support for Actions */
[dir="rtl"] .modern-slide-actions,
html[lang="ar"] .modern-slide-actions {
    justify-content: flex-end !important;
}

.modern-cta-btn {
    background: rgba(100, 181, 246, 0.7) !important;
    padding: 15px 35px !important;
    font-size: 16px !important;
    font-weight: 600 !important;
    border-radius: var(--border-radius-large) !important;
    box-shadow: none !important;
    backdrop-filter: blur(5px) !important;
}

.modern-cta-btn:hover {
    background: rgba(100, 181, 246, 0.9) !important;
    transform: translateY(-2px) !important;
    box-shadow: none !important;
}

.modern-secondary-btn {
    background: rgba(255,255,255,0.15) !important;
    border: 2px solid rgba(255,255,255,0.25) !important;
    backdrop-filter: blur(5px) !important;
}

.modern-secondary-btn:hover {
    background: rgba(255,255,255,0.25) !important;
    border-color: rgba(255,255,255,0.4) !important;
}

.modern-slide-image {
    position: relative !important;
    z-index: 2 !important;
}

.modern-image-container {
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    z-index: 0 !important;
    overflow: hidden !important;
}

.modern-hero-img {
    width: 100% !important;
    height: 100% !important;
    object-fit: cover !important;
    object-position: center !important;
    border-radius: 0 !important;
    box-shadow: none !important;
}

.modern-image-decoration {
    position: absolute !important;
    top: -20px !important;
    right: -20px !important;
    width: 100px !important;
    height: 100px !important;
    background: var(--accent-gradient) !important;
    border-radius: 50% !important;
    opacity: 0.8 !important;
    z-index: -1 !important;
}

/* Modern Overview Section */
.modern-overview-section {
    background: var(--bg-light) !important;
}

.modern-overview-card {
    background: var(--bg-white) !important;
    border-radius: var(--border-radius) !important;
    padding: 30px !important;
    box-shadow: var(--shadow-light) !important;
    transition: var(--transition) !important;
    border: 1px solid var(--border-light) !important;
    height: 100% !important;
}

.modern-overview-card:hover {
    transform: translateY(-8px) !important;
    box-shadow: var(--shadow-colored) !important;
}

.modern-overview-image {
    position: relative !important;
    overflow: hidden !important;
    border-radius: var(--border-radius) !important;
}

.modern-overview-img {
    width: 100% !important;
    height: 200px !important;
    object-fit: cover !important;
    border-radius: var(--border-radius) !important;
    transition: var(--transition) !important;
}

.modern-image-overlay {
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    background: var(--primary-gradient) !important;
    opacity: 0 !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    transition: var(--transition) !important;
    border-radius: var(--border-radius) !important;
}

.modern-overview-card:hover .modern-image-overlay {
    opacity: 0.9 !important;
}

.modern-overlay-icon {
    color: var(--text-white) !important;
    font-size: 3rem !important;
    transform: scale(0.8) !important;
    transition: var(--transition) !important;
}

.modern-overview-card:hover .modern-overlay-icon {
    transform: scale(1) !important;
}

.modern-overview-content {
    padding: 20px 0 0 0 !important;
}

.modern-overview-title {
    font-size: 22px !important;
    font-weight: 700 !important;
    color: var(--text-dark) !important;
    margin-bottom: 15px !important;
}

.modern-overview-desc {
    color: var(--text-light) !important;
    margin-bottom: 25px !important;
    line-height: 1.6 !important;
}

.modern-overview-btn {
    background: var(--primary-gradient) !important;
    padding: 12px 25px !important;
    font-size: 14px !important;
    border-radius: var(--border-radius-large) !important;
}

/* Modern Features Section */
.modern-features-section {
    background: var(--bg-white) !important;
    position: relative !important;
}

.modern-features-section::before {
    content: '' !important;
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    height: 1px !important;
    background: var(--primary-gradient) !important;
}

.modern-section-header {
    margin-bottom: 60px !important;
}

.modern-section-title {
    font-size: 2.5rem !important;
    font-weight: 700 !important;
    color: var(--text-dark) !important;
    margin-bottom: 15px !important;
    position: relative !important;
}

.modern-section-title::after {
    content: '' !important;
    position: absolute !important;
    bottom: -10px !important;
    left: 50% !important;
    transform: translateX(-50%) !important;
    width: 60px !important;
    height: 4px !important;
    background: var(--primary-gradient) !important;
    border-radius: 2px !important;
}

.modern-section-subtitle {
    font-size: 18px !important;
    color: var(--text-light) !important;
    max-width: 600px !important;
    margin: 0 auto !important;
    line-height: 1.6 !important;
}

.modern-features-container {
    background: var(--bg-light) !important;
    border-radius: var(--border-radius) !important;
    padding: 50px 30px !important;
    box-shadow: var(--shadow-light) !important;
}

.modern-feature-card {
    background: var(--bg-white) !important;
    border-radius: var(--border-radius) !important;
    padding: 40px 30px !important;
    text-align: center !important;
    transition: var(--transition) !important;
    position: relative !important;
    overflow: hidden !important;
    border: 1px solid var(--border-light) !important;
    height: 100% !important;
}

.modern-feature-card:hover {
    transform: translateY(-10px) !important;
    box-shadow: var(--shadow-colored) !important;
    border-color: var(--primary-color) !important;
}

.modern-feature-icon {
    margin-bottom: 25px !important;
}

.modern-icon-wrapper {
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    width: 80px !important;
    height: 80px !important;
    background: var(--primary-gradient) !important;
    border-radius: 50% !important;
    margin: 0 auto 20px !important;
    transition: var(--transition) !important;
    box-shadow: var(--shadow-colored) !important;
}

.modern-feature-card:hover .modern-icon-wrapper {
    transform: scale(1.1) rotate(5deg) !important;
    box-shadow: 0 8px 25px rgba(30, 60, 114, 0.4) !important;
}

.modern-icon-wrapper i,
.modern-icon-wrapper svg {
    color: var(--text-white) !important;
    font-size: 2rem !important;
}

.modern-feature-content {
    position: relative !important;
    z-index: 2 !important;
}

.modern-feature-title {
    font-size: 20px !important;
    font-weight: 600 !important;
    color: var(--text-dark) !important;
    margin-bottom: 15px !important;
}

.modern-feature-desc {
    color: var(--text-light) !important;
    line-height: 1.6 !important;
    margin-bottom: 0 !important;
}

.modern-feature-hover-effect {
    position: absolute !important;
    bottom: -100% !important;
    left: 0 !important;
    right: 0 !important;
    height: 4px !important;
    background: var(--primary-gradient) !important;
    transition: var(--transition) !important;
}

.modern-feature-card:hover .modern-feature-hover-effect {
    bottom: 0 !important;
}

/* Modern Products Section */
.modern-products-section {
    background: var(--bg-light) !important;
}

.modern-products-section .modern-section-header {
    margin-bottom: 50px !important;
}

.modern-products-section .modern-section-title {
    color: var(--text-dark) !important;
}

.modern-products-section .modern-section-subtitle {
    color: var(--text-light) !important;
}

/* Responsive Design for Home Page */
@media (max-width: 991px) {
    .modern-slide-title {
        font-size: 2.5rem !important;
    }

    .modern-slide-description {
        font-size: 18px !important;
    }

    .modern-slide-actions {
        justify-content: center !important;
    }

    .modern-section-title {
        font-size: 2rem !important;
    }

    .modern-features-container {
        padding: 30px 20px !important;
    }

    .modern-feature-card {
        padding: 30px 20px !important;
        margin-bottom: 30px !important;
    }
}

@media (max-width: 767px) {
    .modern-slide-item {
        min-height: 500px !important;
    }

    .modern-slide-title {
        font-size: 2rem !important;
    }

    .modern-slide-description {
        font-size: 16px !important;
    }

    .modern-slide-actions {
        flex-direction: column !important;
        align-items: center !important;
    }

    .modern-cta-btn,
    .modern-secondary-btn {
        width: 100% !important;
        max-width: 280px !important;
        justify-content: center !important;
    }

    .modern-section-title {
        font-size: 1.75rem !important;
    }

    .modern-section-subtitle {
        font-size: 16px !important;
    }

    .modern-overview-card {
        margin-bottom: 30px !important;
    }

    .modern-image-decoration {
        width: 60px !important;
        height: 60px !important;
        top: -10px !important;
        right: -10px !important;
    }
}

/* Modern Product Cards */
.modern-products-carousel .owl-item {
    padding: 15px !important;
}

.modern-product-card {
    background: var(--bg-white) !important;
    border-radius: var(--border-radius) !important;
    box-shadow: var(--shadow-light) !important;
    overflow: hidden !important;
    transition: var(--transition) !important;
    border: 1px solid var(--border-light) !important;
    height: 100% !important;
    display: flex !important;
    flex-direction: column !important;
}

.modern-product-card:hover {
    transform: translateY(-8px) !important;
    box-shadow: var(--shadow-colored) !important;
    border-color: var(--primary-color) !important;
}

.modern-product-image {
    position: relative !important;
    overflow: hidden !important;
    background: var(--bg-light) !important;
}

.modern-product-link {
    display: block !important;
    position: relative !important;
}

.modern-product-img {
    width: 100% !important;
    height: 280px !important;
    object-fit: cover !important;
    transition: var(--transition) !important;
}

.modern-product-card:hover .modern-product-img {
    transform: scale(1.05) !important;
}

.modern-product-badge {
    position: absolute !important;
    top: 15px !important;
    left: 15px !important;
    background: var(--primary-gradient) !important;
    color: var(--text-white) !important;
    padding: 6px 12px !important;
    border-radius: var(--border-radius-small) !important;
    font-size: 12px !important;
    font-weight: 600 !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
    box-shadow: var(--shadow-light) !important;
}

.modern-offer-badge {
    position: absolute !important;
    top: 15px !important;
    right: 15px !important;
    background: var(--danger-gradient) !important;
    color: var(--text-white) !important;
    padding: 6px 12px !important;
    border-radius: var(--border-radius-small) !important;
    font-size: 12px !important;
    font-weight: 600 !important;
    display: flex !important;
    align-items: center !important;
    gap: 5px !important;
    box-shadow: var(--shadow-light) !important;
}

.modern-product-actions {
    position: absolute !important;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important;
    display: flex !important;
    gap: 10px !important;
    opacity: 0 !important;
    transition: var(--transition) !important;
    list-style: none !important;
    padding: 0 !important;
    margin: 0 !important;
}

.modern-product-card:hover .modern-product-actions {
    opacity: 1 !important;
}

.modern-action-btn {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    width: 45px !important;
    height: 45px !important;
    background: var(--bg-white) !important;
    border-radius: 50% !important;
    color: var(--text-dark) !important;
    text-decoration: none !important;
    font-size: 18px !important;
    transition: var(--transition) !important;
    box-shadow: var(--shadow-medium) !important;
    position: relative !important;
}

.modern-action-btn:hover {
    background: var(--primary-gradient) !important;
    color: var(--text-white) !important;
    transform: scale(1.1) !important;
    text-decoration: none !important;
}

.modern-action-btn.active {
    background: var(--accent-gradient) !important;
    color: var(--text-white) !important;
}

.modern-check-icon {
    position: absolute !important;
    top: -5px !important;
    right: -5px !important;
    font-size: 14px !important;
    background: var(--success-gradient) !important;
    border-radius: 50% !important;
    width: 18px !important;
    height: 18px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
}

.modern-product-content {
    padding: 25px !important;
    flex: 1 !important;
    display: flex !important;
    flex-direction: column !important;
}

.modern-product-title {
    margin-bottom: 10px !important;
}

.modern-product-name {
    color: var(--text-dark) !important;
    text-decoration: none !important;
    font-size: 18px !important;
    font-weight: 600 !important;
    line-height: 1.3 !important;
    transition: var(--transition) !important;
    display: -webkit-box !important;
    -webkit-line-clamp: 2 !important;
    -webkit-box-orient: vertical !important;
    overflow: hidden !important;
}

.modern-product-name:hover {
    color: var(--primary-color) !important;
    text-decoration: none !important;
}

.modern-product-category {
    color: var(--text-light) !important;
    font-size: 13px !important;
    font-weight: 500 !important;
    margin-bottom: 15px !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
}

.modern-product-rating {
    display: flex !important;
    gap: 3px !important;
    margin-bottom: 15px !important;
    list-style: none !important;
    padding: 0 !important;
}

.modern-product-rating li i {
    color: #ffc107 !important;
    font-size: 14px !important;
}

.modern-product-rating li i.bx-star {
    color: var(--border-light) !important;
}

.modern-product-price {
    margin-bottom: 20px !important;
    display: flex !important;
    align-items: center !important;
    gap: 10px !important;
    flex-wrap: wrap !important;
}

.modern-price-current {
    font-size: 20px !important;
    font-weight: 700 !important;
    color: var(--primary-color) !important;
}

.modern-price-original {
    font-size: 16px !important;
    color: var(--text-light) !important;
    text-decoration: line-through !important;
}

.modern-price-offer {
    font-size: 20px !important;
    font-weight: 700 !important;
    color: var(--primary-color) !important;
}

.modern-discount-badge {
    background: var(--danger-gradient) !important;
    color: var(--text-white) !important;
    padding: 3px 8px !important;
    border-radius: var(--border-radius-small) !important;
    font-size: 11px !important;
    font-weight: 600 !important;
}

.modern-product-actions-bottom {
    margin-top: auto !important;
}

.modern-view-details-btn {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    gap: 8px !important;
    width: 100% !important;
    padding: 12px !important;
    background: var(--primary-gradient) !important;
    color: var(--text-white) !important;
    text-decoration: none !important;
    border-radius: var(--border-radius-small) !important;
    font-weight: 600 !important;
    font-size: 14px !important;
    transition: var(--transition) !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
}

.modern-view-details-btn:hover {
    background: var(--secondary-gradient) !important;
    color: var(--text-white) !important;
    text-decoration: none !important;
    transform: translateY(-2px) !important;
    box-shadow: var(--shadow-colored) !important;
}
