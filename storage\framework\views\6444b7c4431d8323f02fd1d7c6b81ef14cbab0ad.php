<?php $__env->startSection('content'); ?>
<?php
use App\Models\ProductUnits;
use App\Models\Coins;
use App\Models\Countris;
use App\Models\Wishlist;
use App\Models\Compare;
use App\Models\Products;

    if(empty(session()->get('ChangeCountryy'))) {
    
         $Coin=Coins::where('Draw',1)->first();
    $Country=Countris::where('Coin',$Coin->id)->first();
    session()->put('ChangeCountryy',$Country->id);  
      }

$Ses=Countris::find(session()->get('ChangeCountryy'));


use App\Models\HomeEComDesign;
use App\Models\HomeProductEComDesign;
use App\Models\SupPagesEComDesign;
$sub=SupPagesEComDesign::orderBy('id','desc')->first();
$HomeV=HomeEComDesign::orderBy('id','desc')->first();
$HomeVV=HomeProductEComDesign::orderBy('id','desc')->first();

?>


     <title><?php echo e($Comp->Name); ?></title>
        <!-- Start Modern Main Slider Area -->
        <div class="home-slides owl-carousel owl-theme modern-hero-slider">
            <?php $__currentLoopData = $Sliders; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $slide): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <?php if($slide->Type != 1): ?>
                    <div class="main-slider-item modern-slide-item">
                        <div class="modern-image-container">
                            <img src="<?php echo e(URL::to($slide->Image)); ?>" alt="<?php echo e(app()->getLocale() == 'ar' ?$slide->Arabic_Title :$slide->English_Title); ?>" class="modern-hero-img">
                            <div class="modern-slide-overlay"></div>
                        </div>
                        <div class="d-table">
                            <div class="d-table-cell">
                                <div class="container">
                                    <div class="row align-items-center">
                                        <div class="col-lg-12">
                                            <div class="main-slider-content modern-slide-content">
                                                <h1 class="modern-slide-title"><?php echo e(app()->getLocale() == 'ar' ?$slide->Arabic_Title :$slide->English_Title); ?></h1>
                                                <p class="modern-slide-description"><?php echo e(app()->getLocale() == 'ar' ?$slide->Arabic_Desc :$slide->English_Desc); ?></p>

                                                <div class="slider-btn modern-slide-actions">
                                                    <a href="<?php echo e(url('ShopSite')); ?>" class="default-btn modern-cta-btn">
                                                        <i class="bx bx-shopping-bag"></i>
                                                        <?php if(app()->getLocale() == 'ar'): ?> تسوق الآن <?php else: ?> Shop Now <?php endif; ?>
                                                    </a>
                                                    <a href="<?php echo e(url('AboutSite')); ?>" class="default-btn secondary modern-secondary-btn">
                                                        <i class="bx bx-info-circle"></i>
                                                        <?php if(app()->getLocale() == 'ar'): ?> اعرف المزيد <?php else: ?> Learn More <?php endif; ?>
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
        <!-- End Modern Main Slider Area -->

        <!-- Start Modern Overview Area -->
        <section class="overview-area modern-overview-section pt-100 pb-70">
            <div class="container">
                <div class="row">
                    <div class="col-lg-6 col-md-6 mb-4">
                        <div class="single-overview modern-overview-card">
                            <div class="row align-items-center">
                                <div class="col-lg-6">
                                    <div class="overview-image modern-overview-image">
                                        <img src="<?php echo e(URL::to($OneAds->Image)); ?>" alt="ERP Solutions" class="modern-overview-img">
                                        <div class="modern-image-overlay">
                                            <div class="modern-overlay-icon">
                                                <i class="bx bx-trending-up"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-lg-6">
                                    <div class="overview-content modern-overview-content">
                                        <h3 class="modern-overview-title"><?php if(app()->getLocale() == 'ar'): ?> حلول الأعمال <?php else: ?> Business Solutions <?php endif; ?></h3>
                                        <p class="modern-overview-desc"><?php if(app()->getLocale() == 'ar'): ?> حلول ERP شاملة لإدارة أعمالك بكفاءة <?php else: ?> Comprehensive ERP Solutions for efficient business management <?php endif; ?></p>
                                        <div class="overview-btn modern-overview-action">
                                            <a href="<?php echo e(url('ShopSite')); ?>" class="default-btn modern-overview-btn">
                                                <i class="bx bx-store"></i>
                                                <?php if(app()->getLocale() == 'ar'): ?> استكشف الحلول <?php else: ?> Explore Solutions <?php endif; ?>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-6 col-md-6 mb-4">
                        <div class="single-overview modern-overview-card">
                            <div class="row align-items-center">
                                <div class="col-lg-6">
                                    <div class="overview-image modern-overview-image">
                                        <img src="<?php echo e(URL::to($TwoAds->Image)); ?>" alt="Tech Innovation" class="modern-overview-img">
                                        <div class="modern-image-overlay">
                                            <div class="modern-overlay-icon">
                                                <i class="bx bx-rocket"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-lg-6">
                                    <div class="overview-content modern-overview-content">
                                        <h3 class="modern-overview-title"><?php if(app()->getLocale() == 'ar'): ?> مركز الابتكار <?php else: ?> Innovation Hub <?php endif; ?></h3>
                                        <p class="modern-overview-desc"><?php if(app()->getLocale() == 'ar'): ?> تقنيات متطورة لمستقبل أفضل <?php else: ?> Cutting-edge technology for a better future <?php endif; ?></p>
                                        <div class="overview-btn modern-overview-action">
                                            <a href="<?php echo e(url('ShopSite')); ?>" class="default-btn modern-overview-btn">
                                                <i class="bx bx-code-alt"></i>
                                                <?php if(app()->getLocale() == 'ar'): ?> اكتشف المزيد <?php else: ?> Discover More <?php endif; ?>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        <!-- End Modern Overview Area -->

        <!-- Start Modern Support Area -->
        <section class="support-area modern-features-section">
            <div class="container">
                <div class="modern-section-header text-center mb-5">
                    <h2 class="modern-section-title"><?php if(app()->getLocale() == 'ar'): ?> لماذا تختارنا <?php else: ?> Why Choose Us <?php endif; ?></h2>
                    <p class="modern-section-subtitle"><?php if(app()->getLocale() == 'ar'): ?> حلول ERP احترافية مصممة خصيصاً لأعمالك <?php else: ?> Professional ERP solutions designed specifically for your business <?php endif; ?></p>
                </div>

                <div class="support-inner-box modern-features-container">
                    <div class="row">
                        <?php $__currentLoopData = $Befores; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $bef): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="col-lg-3 col-md-6 mb-4">
                            <div class="single-support modern-feature-card">
                                <div class="modern-feature-icon">
                                    <div class="modern-icon-wrapper">
                                        <?php echo $bef->Image; ?>

                                    </div>
                                </div>

                                <div class="support-content modern-feature-content">
                                    <h3 class="modern-feature-title"><?php echo e(app()->getLocale() == 'ar' ?$bef->Arabic_Title :$bef->English_Title); ?></h3>
                                    <p class="modern-feature-desc"><?php if(app()->getLocale() == 'ar'): ?> خدمة احترافية عالية الجودة <?php else: ?> Professional high-quality service <?php endif; ?></p>
                                </div>

                                <div class="modern-feature-hover-effect"></div>
                            </div>
                        </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>
            </div>
        </section>
        <!-- End Modern Support Area -->

        <!-- Start Modern Arrivals Products Area -->
        <section class="arrivals-products-area modern-products-section pt-100 pb-70">
            <div class="container">
                <div class="section-title modern-section-header text-center">
                    <h2 class="modern-section-title"><?php if(app()->getLocale() == 'ar'): ?> أحدث المنتجات <?php else: ?> New Arrivals <?php endif; ?></h2>
                    <p class="modern-section-subtitle"><?php if(app()->getLocale() == 'ar'): ?> اكتشف أحدث الحلول التقنية <?php else: ?> Discover our latest technology solutions <?php endif; ?></p>
                </div>

                <div class="row first owl-carousel owl-theme modern-products-carousel">

                    <?php $__currentLoopData = $ProductsNew; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $pro): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="col-lg-12 col-sm-12 item">
                        <div class="single-arrivals-products modern-product-card">
                            <div class="arrivals-products-image modern-product-image">
                                <a href="<?php echo e(url('ProductDetails/'.$pro->id)); ?>" class="modern-product-link">
                                    <img src="<?php echo e(URL::to($pro->Image)); ?>" alt="<?php echo e(app()->getLocale() == 'ar' ?$pro->P_Ar_Name :$pro->P_En_Name); ?>" class="modern-product-img">
                                </a>

                                <div class="modern-product-badge">
                                    <?php echo e(app()->getLocale() == 'ar' ?$pro->Group()->first()->Name :$pro->Group()->first()->NameEn); ?>

                                </div>

                                <?php if($pro->Offer == 1): ?>
                                <div class="modern-offer-badge">
                                    <i class="bx bx-purchase-tag"></i>
                                    <?php if(app()->getLocale() == 'ar'): ?> عرض خاص <?php else: ?> Special Offer <?php endif; ?>
                                </div>
                                <?php endif; ?>

                                <ul class="modern-product-actions">
                                    <li>
                                        <a href="<?php echo e(url('ProductDetails/'.$pro->id)); ?>" class="modern-action-btn" data-tooltip="<?php if(app()->getLocale() == 'ar'): ?> عرض التفاصيل <?php else: ?> View Details <?php endif; ?>">
                                            <i class="bx bx-show"></i>
                                        </a>
                                    </li>

                                   <?php if(!empty(auth()->guard('client')->user()->id)): ?>
                                    <?php
                                       $Wish=Wishlist::where('Product',$pro->id)
                                            ->where('User',auth()->guard('client')->user()->id)->first();

                                       $Comp=Compare::where('Product',$pro->id)
                                            ->where('User',auth()->guard('client')->user()->id)->first();
                                    ?>

                                    <li>
                                        <?php if(empty($Wish)): ?>
                                        <a href="<?php echo e(url('AddWish/'.$pro->id)); ?>" class="modern-action-btn" data-tooltip="<?php if(app()->getLocale() == 'ar'): ?> أضف للمفضلة <?php else: ?> Add to Wishlist <?php endif; ?>">
                                            <i class="bx bx-heart"></i>
                                        </a>
                                        <?php else: ?>
                                        <span class="modern-action-btn active" data-tooltip="<?php if(app()->getLocale() == 'ar'): ?> في المفضلة <?php else: ?> In Wishlist <?php endif; ?>">
                                            <i class="bx bxs-heart"></i>
                                        </span>
                                        <?php endif; ?>
                                    </li>
                                    <li>
                                       <?php if(empty($Comp)): ?>
                                        <a href="<?php echo e(url('AddCompare/'.$pro->id)); ?>" class="modern-action-btn" data-tooltip="<?php if(app()->getLocale() == 'ar'): ?> مقارنة <?php else: ?> Compare <?php endif; ?>">
                                            <i class="bx bx-git-compare"></i>
                                        </a>
                                        <?php else: ?>
                                        <span class="modern-action-btn active" data-tooltip="<?php if(app()->getLocale() == 'ar'): ?> في المقارنة <?php else: ?> In Compare <?php endif; ?>">
                                            <i class="bx bx-git-compare"></i>
                                            <i class="bx bx-check-circle modern-check-icon"></i>
                                        </span>
                                        <?php endif; ?>
                                    </li>
                          <?php endif; ?>
                                </ul>
                            </div>

                            <div class="arrivals-products-content modern-product-content">
                                <h3 class="modern-product-title">
                                    <a href="<?php echo e(url('ProductDetails/'.$pro->id)); ?>" class="modern-product-name">
                                        <?php echo e(app()->getLocale() == 'ar' ?$pro->P_Ar_Name :$pro->P_En_Name); ?>

                                    </a>
                                </h3>

                                <div class="modern-product-category">
                                    <?php echo e(app()->getLocale() == 'ar' ?$pro->Group()->first()->Name :$pro->Group()->first()->NameEn); ?>

                                </div>

                                <ul class="modern-product-rating">
                                    <?php if(empty($pro->rate)): ?>
                                        <?php for($i = 1; $i <= 5; $i++): ?>
                                            <li><i class='bx bx-star'></i></li>
                                        <?php endfor; ?>
                                    <?php else: ?>
                                        <?php for($i = 1; $i <= 5; $i++): ?>
                                            <?php if($i <= $pro->rate): ?>
                                                <li><i class='bx bxs-star'></i></li>
                                            <?php else: ?>
                                                <li><i class='bx bx-star'></i></li>
                                            <?php endif; ?>
                                        <?php endfor; ?>
                                    <?php endif; ?>
                                </ul>

                                <div class="modern-product-price">
                                    <?php
                                        $Price = ProductUnits::where('Product',$pro->id)->where('Def',1)->first();
                                    ?>

                                    <?php if($pro->Offer == 1): ?>
                                        <span class="modern-price-original">
                                            <?php echo e($Ses->Coin()->first()->Symbol); ?><?php echo e(round($Price->Price / $Ses->Coin()->first()->Draw)); ?>

                                        </span>
                                        <span class="modern-price-offer">
                                            <?php echo e($Ses->Coin()->first()->Symbol); ?><?php echo e(round($pro->OfferPrice / $Ses->Coin()->first()->Draw)); ?>

                                        </span>
                                        <span class="modern-discount-badge">
                                            -<?php echo e(round((($Price->Price - $pro->OfferPrice) / $Price->Price) * 100)); ?>%
                                        </span>
                                    <?php else: ?>
                                        <span class="modern-price-current">
                                            <?php echo e($Ses->Coin()->first()->Symbol); ?><?php echo e(round($Price->Price / $Ses->Coin()->first()->Draw)); ?>

                                        </span>
                                    <?php endif; ?>
                                </div>

                                <div class="modern-product-actions-bottom">
                                    <a href="<?php echo e(url('ProductDetails/'.$pro->id)); ?>" class="modern-view-details-btn">
                                        <i class="bx bx-show"></i>
                                        <?php if(app()->getLocale() == 'ar'): ?> عرض التفاصيل <?php else: ?> View Details <?php endif; ?>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                   
                </div>
            </div>
        </section>
        <!-- End Arrivals Products Area -->

        <!-- Start Offer Products Area -->
        <section class="offer-products-area pb-70">
            <div class="container">
                <div class="section-title modern-section-header text-center">
                    <h2 class="modern-section-title"><?php if(app()->getLocale() == 'ar'): ?> عروض خاصة <?php else: ?> Special Offers <?php endif; ?></h2>
                    <p class="modern-section-subtitle"><?php if(app()->getLocale() == 'ar'): ?> اكتشف أفضل العروض والخصومات <?php else: ?> Discover the best deals and discounts <?php endif; ?></p>
                </div>

             
                <div class="row  first owl-carousel owl-theme">
                    
               <?php $__currentLoopData = $ProductsOffers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $pro): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>        
              <div class="col-lg-12 col-md-12 item">
                        <div class="single-offer-products">
                            <div class="offer-products-image">
                                <a href="<?php echo e(url('ProductDetails/'.$pro->id)); ?>"><img src="<?php echo e(URL::to($pro->Image)); ?>" alt="image"></a>
                            </div>
        
                            <div class="offer-products-content">
              <span style="color: <?php echo e($HomeVV->Special_Offer_Product_Txt_Color); ?>"> <?php echo e(app()->getLocale() == 'ar' ?$pro->Group()->first()->Name :$pro->Group()->first()->NameEn); ?>    </span>
                                <h3>
           <a href="<?php echo e(url('ProductDetails/'.$pro->id)); ?>" style="color: <?php echo e($HomeVV->Special_Offer_Product_Txt_Color); ?>"><?php echo e(app()->getLocale() == 'ar' ?$pro->P_Ar_Name :$pro->P_En_Name); ?></a>
                                </h3>
                                <div class="price">
                                    
                                    
                                         <?php  $Price=ProductUnits::where('Product',$pro->id)->where('Def',1)->first();   ?>  
     <?php if($pro->Offer == 1): ?>
      
 <span style="color: <?php echo e($HomeVV->Special_Offer_Product_Price_Color); ?>" class="new-price"><?php echo e($Ses->Coin()->first()->Symbol); ?><?php echo e(round($pro->OfferPrice / $Ses->Coin()->first()->Draw)); ?></span>
    <span style="color: <?php echo e($HomeVV->Special_Offer_Product_Price_Color); ?>" class="old-price"><?php echo e($Ses->Coin()->first()->Symbol); ?><?php echo e(round($Price->Price / $Ses->Coin()->first()->Draw)); ?></span>            
                                    
        <?php else: ?>

         <span style="color: <?php echo e($HomeVV->Special_Offer_Product_Price_Color); ?>" class="new-price"><?php echo e($Ses->Coin()->first()->Symbol); ?><?php echo e(round($Price->Price / $Ses->Coin()->first()->Draw)); ?></span>                            
        <?php endif; ?>   
         
                                </div>
                                <ul class="rating">
                                    <li>
                                                  <?php if(empty($pro->rate)): ?>
                                            <i style="color: <?php echo e($HomeVV->Special_Offer_Product_Rate_Color); ?>" class='bx bx-star'></i>
                                        <i style="color: <?php echo e($HomeVV->Special_Offer_Product_Rate_Color); ?>" class='bx bx-star'></i>
                                        <i style="color: <?php echo e($HomeVV->Special_Offer_Product_Rate_Color); ?>" class='bx bx-star'></i>
                                        <i style="color: <?php echo e($HomeVV->Special_Offer_Product_Rate_Color); ?>" class='bx bx-star'></i>
                                        <i style="color: <?php echo e($HomeVV->Special_Offer_Product_Rate_Color); ?>" class='bx bx-star'></i>
                                            <?php elseif($pro->rate == 1): ?> 
                                     <i style="color: <?php echo e($HomeVV->Special_Offer_Product_Rate_Color); ?>" class='bx bxs-star'></i>
                                        <i style="color: <?php echo e($HomeVV->Special_Offer_Product_Rate_Color); ?>" class='bx bx-star'></i>
                                        <i style="color: <?php echo e($HomeVV->Special_Offer_Product_Rate_Color); ?>" class='bx bx-star'></i>
                                        <i style="color: <?php echo e($HomeVV->Special_Offer_Product_Rate_Color); ?>" class='bx bx-star'></i>
                                        <i style="color: <?php echo e($HomeVV->Special_Offer_Product_Rate_Color); ?>" class='bx bx-star'></i>
                                            <?php elseif($pro->rate == 2): ?>  
                                 <i style="color: <?php echo e($HomeVV->Special_Offer_Product_Rate_Color); ?>" class='bx bxs-star'></i>
                                        <i style="color: <?php echo e($HomeVV->Special_Offer_Product_Rate_Color); ?>" class='bx bxs-star'></i>
                                        <i style="color: <?php echo e($HomeVV->Special_Offer_Product_Rate_Color); ?>" class='bx bx-star'></i>
                                        <i style="color: <?php echo e($HomeVV->Special_Offer_Product_Rate_Color); ?>" class='bx bx-star'></i>
                                        <i style="color: <?php echo e($HomeVV->Special_Offer_Product_Rate_Color); ?>" class='bx bx-star'></i>
                                            <?php elseif($pro->rate == 3): ?> 
                                  <i style="color: <?php echo e($HomeVV->Special_Offer_Product_Rate_Color); ?>" class='bx bxs-star'></i>
                                        <i style="color: <?php echo e($HomeVV->Special_Offer_Product_Rate_Color); ?>" class='bx bxs-star'></i>
                                        <i style="color: <?php echo e($HomeVV->Special_Offer_Product_Rate_Color); ?>" class='bx bxs-star'></i>
                                        <i style="color: <?php echo e($HomeVV->Special_Offer_Product_Rate_Color); ?>" class='bx bx-star'></i>
                                        <i style="color: <?php echo e($HomeVV->Special_Offer_Product_Rate_Color); ?>" class='bx bx-star'></i>
                                            <?php elseif($pro->rate == 4): ?>  
                                  <i style="color: <?php echo e($HomeVV->Special_Offer_Product_Rate_Color); ?>" class='bx bxs-star'></i>
                                        <i style="color: <?php echo e($HomeVV->Special_Offer_Product_Rate_Color); ?>" class='bx bxs-star'></i>
                                        <i style="color: <?php echo e($HomeVV->Special_Offer_Product_Rate_Color); ?>" class='bx bxs-star'></i>
                                        <i style="color: <?php echo e($HomeVV->Special_Offer_Product_Rate_Color); ?>" class='bx bxs-star'></i>
                                        <i style="color: <?php echo e($HomeVV->Special_Offer_Product_Rate_Color); ?>" class='bx bx-star'></i>
                                            <?php elseif($pro->rate == 5): ?>
                                     <i style="color: <?php echo e($HomeVV->Special_Offer_Product_Rate_Color); ?>" class='bx bxs-star'></i>
                                        <i style="color: <?php echo e($HomeVV->Special_Offer_Product_Rate_Color); ?>" class='bx bxs-star'></i>
                                        <i style="color: <?php echo e($HomeVV->Special_Offer_Product_Rate_Color); ?>" class='bx bxs-star'></i>
                                        <i style="color: <?php echo e($HomeVV->Special_Offer_Product_Rate_Color); ?>" class='bx bxs-star'></i>
                                        <i style="color: <?php echo e($HomeVV->Special_Offer_Product_Rate_Color); ?>" class='bx bxs-star'></i>
                                            <?php endif; ?>  
                                    
                                           </li> 
                                </ul>
                            </div>
                        </div>
                    </div>
                   <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            </div>
        </section>
        <!-- End Offer Products Area -->

        <!-- Start Collection Area -->
        <section class="collection-area">
            <div class="container">
                <div class="collection-inner-box">
                    <div class="row align-items-center">
                        <div class="col-lg-4 col-md-6">
                            <div class="collection-image">
                                <img src="<?php echo e(URL::to($ThreeAds->Image)); ?>" alt="image">
                            </div>
                        </div>

                        <div class="col-lg-4 col-md-6">
                            <div class="collection-content">

                                <div class="collection-btn">
                                    <a href="<?php echo e(url('ShopSite')); ?>" class="default-btn">
                                        <i class="bx bx-shopping-bag"></i>
                                        <?php if(app()->getLocale() == 'ar'): ?> تسوق الآن <?php else: ?> Shop Now <?php endif; ?>
                                        <span></span>
                                    </a>
                                </div>
                            </div>
                        </div>

                        <div class="col-lg-4 col-md-6 offset-lg-0 offset-md-3">
                            <div class="collection-image">
                                <img src="<?php echo e(URL::to($FourAds->Image)); ?>" alt="image">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        <!-- End Collection Area -->

   
        <!-- Start Bestsellers Area -->
        <section class="bestsellers-area pt-100 pb-70">
            <div class="container">
                <div class="section-title" style=" border-bottom:1px solid <?php echo e($HomeVV->Best_Sellers_Title_Txt_Color); ?>" >
                    <h2 style=" background: :<?php echo e($HomeVV->Best_Sellers_Title_BG_Color); ?>; color: <?php echo e($HomeVV->Best_Sellers_Title_Txt_Color); ?>" >Bestsellers</h2>
                </div>
        
                <div class="tab bestsellers-list-tab">
                    <ul class="tabs">
                        
                    <?php $__currentLoopData = $Groups; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $grop): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>    
                        <li>
                            <a href="#"><?php echo e(app()->getLocale() == 'ar' ?$grop->Name :$grop->NameEn); ?></a>
                        </li>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>    
               
                    </ul>

                    <div class="tab_content">
                               <?php $__currentLoopData = $Groups; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $grop): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?> 
                        <?php  
                        
                        $ProductsMost=Products::whereIn('Store_Show',[1,3])
      ->whereIn('P_Type',['Completed','Single_Variable','Duble_Variable'])
            ->where('Store_Type',2)
            ->where('Group',$grop->id)
    ->orderBy('id','desc')->take(50)->get();    
                        
                        ?>
                        <div class="tabs_item">
                            <div class="row">
                                
                                         <?php $__currentLoopData = $ProductsMost; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $pro): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>  
                                <div class="col-lg-3 col-sm-6">
                                    <div class="single-bestsellers-products">
                                        <div class="bestsellers-products-image">
                                            <a href="<?php echo e(url('ProductDetails/'.$pro->id)); ?>"><img src="<?php echo e(URL::to($pro->Image)); ?>" alt="image"></a>
                                            <div class="tag">  <?php echo e(app()->getLocale() == 'ar' ?$pro->Group()->first()->Name :$pro->Group()->first()->NameEn); ?></div>
                                            <ul class="bestsellers-action">
                                                 <li>
                                        <a href="<?php echo e(url('ProductDetails/'.$pro->id)); ?>">
                                            <i class="flaticon-shopping-cart"></i>
                                        </a>
                                    </li>
                                    
                                   <?php if(!empty(auth()->guard('client')->user()->id)): ?>            
                                    <?php            
                                       $Wish=Wishlist::where('Product',$pro->id)
                                            ->where('User',auth()->guard('client')->user()->id)->first(); 
                                       
                                                       $Comp=Compare::where('Product',$pro->id)
                                            ->where('User',auth()->guard('client')->user()->id)->first(); 
                                                
                                    ?>
          
                                    <li>
                                        
                                        <?php if(empty($Wish)): ?>     
                                        <a href="<?php echo e(url('AddWish/'.$pro->id)); ?>"><i class="flaticon-heart"></i></a>
                                             <?php else: ?>   
                                       
                                        <i style="color:<?php echo e($HomeVV->Best_Sellers_Product_Icon_Txt_Color); ?>" class="bx bxs-heart"></i>
                                            <?php endif; ?> 
                                    </li>
                                    <li>
                                       <?php if(empty($Comp)): ?>      
                                        <a href="<?php echo e(url('AddCompare/'.$pro->id)); ?>"><i class="bx bx-git-compare"></i></a>
                                        <?php else: ?>
                                        
                                        <i style="color:<?php echo e($HomeVV->Best_Sellers_Product_Icon_Txt_Color); ?>" class="bx bx-git-compare">  <i style="color:<?php echo e($HomeVV->Best_Sellers_Product_Icon_Txt_Color); ?>" class="bx bx-badge-check"></i></i>
                                      
                                          <?php endif; ?>  
                                    </li>
                          <?php endif; ?>    
                                            </ul>
                                        </div>
            
                                        <div class="bestsellers-products-content">
                                            <h3>
      <a href="<?php echo e(url('ProductDetails/'.$pro->id)); ?>"  style="color:<?php echo e($HomeVV->Best_Sellers_Product_Txt_Color); ?>">   <?php echo e(app()->getLocale() == 'ar' ?$pro->P_Ar_Name :$pro->P_En_Name); ?>     </a>
                                            </h3>
                                            <ul class="rating">
                                             <?php if(empty($pro->rate)): ?>
                                     <li><i style="color:<?php echo e($HomeVV->Best_Sellers_Product_Rate_Color); ?>" class='bx bx-star'></i></li>
                                    <li><i  style="color:<?php echo e($HomeVV->Best_Sellers_Product_Rate_Color); ?>" class='bx bx-star'></i></li>
                                    <li><i  style="color:<?php echo e($HomeVV->Best_Sellers_Product_Rate_Color); ?>" class='bx bx-star'></i></li>
                                    <li><i  style="color:<?php echo e($HomeVV->Best_Sellers_Product_Rate_Color); ?>" class='bx bx-star'></i></li>
                                    <li><i  style="color:<?php echo e($HomeVV->Best_Sellers_Product_Rate_Color); ?>" class='bx bx-star'></i></li>
                                            <?php elseif($pro->rate == 1): ?> 
                                     <li><i  style="color:<?php echo e($HomeVV->Best_Sellers_Product_Rate_Color); ?>" class='bx bxs-star'></i></li>
                                    <li><i  style="color:<?php echo e($HomeVV->Best_Sellers_Product_Rate_Color); ?>" class='bx bx-star'></i></li>
                                    <li><i  style="color:<?php echo e($HomeVV->Best_Sellers_Product_Rate_Color); ?>" class='bx bx-star'></i></li>
                                    <li><i  style="color:<?php echo e($HomeVV->Best_Sellers_Product_Rate_Color); ?>" class='bx bx-star'></i></li>
                                    <li><i  style="color:<?php echo e($HomeVV->Best_Sellers_Product_Rate_Color); ?>" class='bx bx-star'></i></li>
                                            <?php elseif($pro->rate == 2): ?>  
                                     <li><i  style="color:<?php echo e($HomeVV->Best_Sellers_Product_Rate_Color); ?>" class='bx bxs-star'></i></li>
                                    <li><i  style="color:<?php echo e($HomeVV->Best_Sellers_Product_Rate_Color); ?>" class='bx bxs-star'></i></li>
                                    <li><i  style="color:<?php echo e($HomeVV->Best_Sellers_Product_Rate_Color); ?>" class='bx bx-star'></i></li>
                                    <li><i  style="color:<?php echo e($HomeVV->Best_Sellers_Product_Rate_Color); ?>" class='bx bx-star'></i></li>
                                    <li><i  style="color:<?php echo e($HomeVV->Best_Sellers_Product_Rate_Color); ?>" class='bx bx-star'></i></li>
                                            <?php elseif($pro->rate == 3): ?> 
                                     <li><i  style="color:<?php echo e($HomeVV->Best_Sellers_Product_Rate_Color); ?>" class='bx bxs-star'></i></li>
                                    <li><i  style="color:<?php echo e($HomeVV->Best_Sellers_Product_Rate_Color); ?>" class='bx bxs-star'></i></li>
                                    <li><i  style="color:<?php echo e($HomeVV->Best_Sellers_Product_Rate_Color); ?>" class='bx bxs-star'></i></li>
                                    <li><i  style="color:<?php echo e($HomeVV->Best_Sellers_Product_Rate_Color); ?>" class='bx bx-star'></i></li>
                                    <li><i  style="color:<?php echo e($HomeVV->Best_Sellers_Product_Rate_Color); ?>" class='bx bx-star'></i></li>
                                            <?php elseif($pro->rate == 4): ?>  
                                     <li><i  style="color:<?php echo e($HomeVV->Best_Sellers_Product_Rate_Color); ?>" class='bx bxs-star'></i></li>
                                    <li><i  style="color:<?php echo e($HomeVV->Best_Sellers_Product_Rate_Color); ?>" class='bx bxs-star'></i></li>
                                    <li><i  style="color:<?php echo e($HomeVV->Best_Sellers_Product_Rate_Color); ?>" class='bx bxs-star'></i></li>
                                    <li><i  style="color:<?php echo e($HomeVV->Best_Sellers_Product_Rate_Color); ?>" class='bx bxs-star'></i></li>
                                    <li><i  style="color:<?php echo e($HomeVV->Best_Sellers_Product_Rate_Color); ?>" class='bx bx-star'></i></li>
                                            <?php elseif($pro->rate == 5): ?>
                                     <li><i  style="color:<?php echo e($HomeVV->Best_Sellers_Product_Rate_Color); ?>" class='bx bxs-star'></i></li>
                                    <li><i  style="color:<?php echo e($HomeVV->Best_Sellers_Product_Rate_Color); ?>" class='bx bxs-star'></i></li>
                                    <li><i  style="color:<?php echo e($HomeVV->Best_Sellers_Product_Rate_Color); ?>" class='bx bxs-star'></i></li>
                                    <li><i  style="color:<?php echo e($HomeVV->Best_Sellers_Product_Rate_Color); ?>" class='bx bxs-star'></i></li>
                                    <li><i  style="color:<?php echo e($HomeVV->Best_Sellers_Product_Rate_Color); ?>" class='bx bxs-star'></i></li>
                                            <?php endif; ?>  
                                            </ul>
                                                                     <span style="color:<?php echo e($HomeVV->Best_Sellers_Product_Price_Color); ?>">
                                
     <?php  $Price=ProductUnits::where('Product',$pro->id)->where('Def',1)->first();   ?>  
     <?php if($pro->Offer == 1): ?>
<del style="color:<?php echo e($HomeVV->Best_Sellers_Product_Price_Color); ?>"><?php echo e($Ses->Coin()->first()->Symbol); ?><?php echo e(round($Price->Price / $Ses->Coin()->first()->Draw)); ?></del>
<?php echo e($Ses->Coin()->first()->Symbol); ?><?php echo e(round($pro->OfferPrice / $Ses->Coin()->first()->Draw)); ?>

        <?php else: ?>
<?php echo e($Ses->Coin()->first()->Symbol); ?><?php echo e(round($Price->Price / $Ses->Coin()->first()->Draw)); ?>

        <?php endif; ?>     
                                
                                
                                </span>
                                        </div>
                                    </div>
                                </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                
                              
                                
                              

                            </div>
                        </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                     
                    </div>
                </div>
            </div>
        </section>
        <!-- End Bestsellers Area -->


        <!-- Start Blog Area -->
        <section class="blog-area pb-70">
            <div class="container">
                <div class="section-title modern-section-header text-center">
                    <h2 class="modern-section-title"><?php if(app()->getLocale() == 'ar'): ?> أحدث المقالات <?php else: ?> Latest Blog <?php endif; ?></h2>
                    <p class="modern-section-subtitle"><?php if(app()->getLocale() == 'ar'): ?> اطلع على آخر الأخبار والمقالات التقنية <?php else: ?> Stay updated with our latest news and tech articles <?php endif; ?></p>
                </div>

                <div class="row owl-carousel new-carousel">
                    
                         <?php $__currentLoopData = $Articles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $art): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="col-lg-12 item col-md-6 ">
                        <div class="single-blog">
                            <div class="blog-image">
                                <a href="<?php echo e(url('BlogDetails/'.$art->id)); ?>">
                                    <img src="<?php echo e(URL::to($art->Sub_Image)); ?>" alt="image">
                                </a>
                            </div>



                            <div class="blog-content">
                            
                                <h3>
                <a style="color: <?php echo e($sub->Blogs_Title_Color); ?>" href="<?php echo e(url('BlogDetails/'.$art->id)); ?>">       <?php echo e(app()->getLocale() == 'ar' ?$art->Arabic_Title :$art->English_Title); ?></a>
                                </h3>
                                <div class="post-meta">
                                    <a style="color: <?php echo e($sub->Blogs_Txt_Color); ?>" href="<?php echo e(url('BlogDetails/'.$art->id)); ?>"><?php echo e($art->Date); ?></a> 
                                </div>
                                <p style="display: none">       <?php echo app()->getLocale() == 'ar' ?$art->Arabic_Desc :$art->English_Desc; ?></p>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                </div>
            </div>
        </section>
        <!-- End Blog Area -->

        <!-- Start Partner Area -->
        <div class="partner-area ptb-100" style="background: <?php echo e($HomeV->Partners_BG_Color); ?>">
            <div class="container">
                <div class="partner-slider owl-carousel owl-theme">
                    
                   <?php $__currentLoopData = $Brands; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $brand): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
   
                    <div class="partner-item">
                        <a href="<?php echo e(url('ShopFilterBrand/'.$brand->id)); ?>">
                            <img src="<?php echo e(URL::to($brand->Image)); ?> " alt="image">
                        </a>
                    </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                   
                </div>
            </div>
        </div>
        <!-- End Partner Area -->

       
            <!-- Slider -->
            <style>
    .slider-btn .default-btn{
        
        background: <?php echo e($HomeV->Slider_Button_BG_Color); ?>  !important;
        color: <?php echo e($HomeV->Slider_Button_Txt_Color); ?>  !important;
    }
    
    
      .slider-btn .default-btn:hover{
        
        background: <?php echo e($HomeV->Slider_Button_Hover_BG_Color); ?>  !important;
        color: <?php echo e($HomeV->Slider_Button_Hover_Txt_Color); ?>  !important;
    }
    
    .slider-btn .default-btn:hover i {

        color: <?php echo e($HomeV->Slider_Button_Hover_Txt_Color); ?>  !important;
}
    
    
    .slider-btn .default-btn:hover span {
        
             background: <?php echo e($HomeV->Slider_Button_Hover_BG_Color); ?>  !important;
        color: <?php echo e($HomeV->Slider_Button_Hover_Txt_Color); ?>  !important;
        
    }
</style>  
            
            <!-- Ads Top -->
            <style>
            .single-overview {
    background-color: <?php echo e($HomeV->Ads_Top_Img_First_BG_Color); ?> !important;
                }
                
                .single-overview::before {
                    
              background-color: <?php echo e($HomeV->Ads_Top_Img_First_Before_BG_Color); ?> !important;         
                }
                
                
                       .w {
    background-color: <?php echo e($HomeV->Ads_Top_Img_Second_BG_Color); ?> !important;
                }
                
                .w::before {
                    
              background-color: <?php echo e($HomeV->Ads_Top_Img_Second_Before_BG_Color); ?> !important;         
                }
                
                
                
                  .single-overview .default-btn{
        
        background: <?php echo e($HomeV->Ads_Top_Img_Button_BG_Color); ?>  !important;
        color: <?php echo e($HomeV->Ads_Top_Img_Button_Txt_Color); ?>  !important;
    }
    
    
      .single-overview .default-btn:hover{
        
        background: <?php echo e($HomeV->Ads_Top_Img_Button_Hover_BG_Color); ?>  !important;
        color: <?php echo e($HomeV->Ads_Top_Img_Button_Hover_Txt_Color); ?>  !important;
    }
    
    .single-overview .default-btn:hover i {

        color: <?php echo e($HomeV->Ads_Top_Img_Button_Hover_Txt_Color); ?>  !important;
}
    
    
    .single-overview .default-btn:hover span {
        
             background: <?php echo e($HomeV->Ads_Top_Img_Button_Hover_BG_Color); ?>  !important;
        color: <?php echo e($HomeV->Ads_Top_Img_Button_Hover_Txt_Color); ?>  !important;
        
    }
                
            </style>
            
              <!-- Support Icons -->
            <style>
                .single-support .icon i {
                    
                    color: <?php echo e($HomeV->Support_Icons_Color); ?> !important;
                    
                }
            </style>
            
                 <!-- Ads Bottom -->
            <style>
            
                .collection-inner-box{
                   
                     background-color: <?php echo e($HomeV->Ads_Bootom_Imgs_BG_Color); ?> !important;
                    
                }
                
                .collection-inner-box::before {
                    
              background-color: <?php echo e($HomeV->Ads_Bootom_Imgs_Middle_BG_Color); ?> !important;         
                    
                }
                
                
                   .collection-inner-box .default-btn{
        
        background: <?php echo e($HomeV->Ads_Bootom_Imgs_Button_BG_Color); ?>  !important;
        color: <?php echo e($HomeV->Ads_Bootom_Imgs_Button_Txt_Color); ?>  !important;
    }
    
    
      .collection-inner-box .default-btn:hover{
        
        background: <?php echo e($HomeV->Ads_Bootom_Imgs_Button_Hover_BG_Color); ?>  !important;
        color: <?php echo e($HomeV->Ads_Bootom_Imgs_Button_Hover_Txt_Color); ?>  !important;
    }
    
    .collection-inner-box .default-btn:hover i {

        color: <?php echo e($HomeV->Ads_Bootom_Imgs_Button_Hover_Txt_Color); ?>  !important;
}
    
    
    .collection-inner-box .default-btn:hover span {
        
             background: <?php echo e($HomeV->Ads_Bootom_Imgs_Button_Hover_BG_Color); ?>  !important;
        color: <?php echo e($HomeV->Ads_Bootom_Imgs_Button_Hover_Txt_Color); ?>  !important;
        
    }
            </style> 

               <!-- Blogs -->
            <style>
                .single-blog .blog-content h3 a:hover {
                    
                    color: <?php echo e($sub->Blogs_Hover_Txt_Color); ?> !important;   
                }
                
                   .single-blog .blog-content .post-meta a:hover {
                    
                    color: <?php echo e($sub->Blogs_Hover_Txt_Color); ?> !important;   
                }
            </style>
            
            <!-- Special Offer -->
            <style>
                    single-offer-products {
                        border: 1px solid <?php echo e($HomeVV->Special_Offer_Product_Border_Color); ?> !important;
                        background-color: <?php echo e($HomeVV->Special_Offer_Product_BG_Color); ?> !important;
                    }
                     
                     .single-offer-products .offer-products-content h3 a:hover {
                         
                         color: <?php echo e($HomeVV->Special_Offer_Product_Txt_Hover_Color); ?> !important;
                     }
                </style>  
            
            <!-- Best Sellers -->
              <style>
                .bestsellers-list-tab .tabs li a{
                    
                    color: <?php echo e($HomeVV->Best_Sellers_Category_Txt_Color); ?> !important;
                }  
                
                .bestsellers-list-tab .tabs li.current a {
      color: <?php echo e($HomeVV->Best_Sellers_Category_Active_Txt_Color); ?> !important;
    border-bottom: 1px solid <?php echo e($HomeVV->Best_Sellers_Category_Active_Txt_Color); ?> !important;
}
                
                
        .single-bestsellers-products .bestsellers-products-image .tag {

    background: <?php echo e($HomeVV->Best_Sellers_Product_Group_BG_Color); ?> !important;
    color: <?php echo e($HomeVV->Best_Sellers_Product_Group_Txt_Color); ?> !important;
            
                }
                
                      .single-bestsellers-products .bestsellers-products-image .tag:hover {

    background: <?php echo e($HomeVV->Best_Sellers_Product_Group_Hover_BG_Color); ?> !important;
    color: <?php echo e($HomeVV->Best_Sellers_Product_Group_Hover_Txt_Color); ?> !important;
            
                }
                
                .single-bestsellers-products {
                    
             background: <?php echo e($HomeVV->Best_Sellers_Product_BG_Color); ?> !important;         
                }
                
                
                .single-bestsellers-products .bestsellers-products-image .bestsellers-action li a i {

                    background-color:<?php echo e($HomeVV->Best_Sellers_Product_Icon_BG_Color); ?> !important;
                    color: <?php echo e($HomeVV->Best_Sellers_Product_Icon_Txt_Color); ?> !important;
                
                }
                
                            .single-bestsellers-products .bestsellers-products-image .bestsellers-action li a i:hover {

                                background-color:<?php echo e($HomeVV->Best_Sellers_Product_Icon_Hover_BG_Color); ?> !important;
                                color: <?php echo e($HomeVV->Best_Sellers_Product_Icon_Hover_Txt_Color); ?> !important;
                
                }
            </style>
            
                <!-- New Arrivals -->
            <style>
                .single-arrivals-products .arrivals-products-image .tag{
                
                        background: <?php echo e($HomeVV->New_Arrivals_Product_Group_BG_Color); ?> !important;
    color: <?php echo e($HomeVV->New_Arrivals_Product_Group_Txt_Color); ?> !important;
                    
                }
                
                .single-arrivals-products .arrivals-products-image .tag:hover{
                  
                                background: <?php echo e($HomeVV->New_Arrivals_Product_Group_Hover_BG_Color); ?> !important;
    color: <?php echo e($HomeVV->New_Arrivals_Product_Group_Hover_Txt_Color); ?> !important;
                    
                }
                
                
                .single-arrivals-products .arrivals-products-image .arrivals-action li a i{
                    
                         background-color:<?php echo e($HomeVV->New_Arrivals_Product_Icon_BG_Color); ?> !important;
                    color: <?php echo e($HomeVV->New_Arrivals_Product_Icon_Txt_Color); ?> !important;
                    
                }        
                
                
                .single-arrivals-products .arrivals-products-image .arrivals-action li a i:hover{
                    
                         background-color:<?php echo e($HomeVV->New_Arrivals_Product_Icon_Hover_BG_Color); ?> !important;
                    color: <?php echo e($HomeVV->New_Arrivals_Product_Icon_Hover_Txt_Color); ?> !important;
                    
                }              
                
                .single-arrivals-products .arrivals-products-content span:hover {

                   color: <?php echo e($HomeVV->New_Arrivals_Product_Hover_Price_Color); ?> !important;   
                }



            
            </style>

<?php $__env->stopSection(); ?>    
<?php echo $__env->make('site.index', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\ost-erp-online\resources\views/site/home.blade.php ENDPATH**/ ?>