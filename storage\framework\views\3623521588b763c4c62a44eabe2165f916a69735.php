   
<?php $__env->startSection('content'); ?>
<?php
use App\Models\MainEComDesign;
use App\Models\SupPagesPartTwoEComDesign;
$main=MainEComDesign::orderBy('id','desc')->first();
$sub=SupPagesPartTwoEComDesign::orderBy('id','desc')->first();
?>

 <style>
.page-title-area {
    background-color: <?php echo e($main->Breadcumb_BG_Color); ?> !important;
}
     
    .register-area {
      background-color: <?php echo e($main->Sub_Page_BG_Color); ?> !important;
} 
     
     
       .register-form{
         
         background: <?php echo e($sub->Reg_Login_Form_Box_BG_Color); ?> !important;
     }
     
         .register-form h2 {

    color:  <?php echo e($sub->Reg_Login_Form_Box_Title_Color); ?> !important;
     }
     
     .register-form h2::before {

    border-bottom: 1px solid <?php echo e($sub->Reg_Login_Form_Box_Title_Color); ?> !important;
     }
     
     .register-form form .form-group .form-control {
    color: <?php echo e($sub->Reg_Login_Form_Box_Input_Border_Color); ?> !important;
    border: 1px solid <?php echo e($sub->Reg_Login_Form_Box_Input_Border_Color); ?> !important;
     }
     
     .register-form form .lost-your-password a{
         
           color: <?php echo e($sub->Reg_Login_Form_Box_Txt_Color); ?> !important;
     }
     
     
     .register-form form .lost-your-password a::before{
         
         background: <?php echo e($sub->Reg_Login_Form_Box_Txt_Color); ?> !important;
     }
     
       .register-form form .lost-your-password a::before:hover{
         
         background: <?php echo e($sub->Reg_Login_Form_Box_Txt_Hover_Color); ?> !important;
     }
     
     
     .register-form .important-text p a{
         
            color: <?php echo e($sub->Reg_Login_Form_Box_Txt_Color); ?> !important; 
        
         
     }
     
     
          
    .register-form .important-text p{
         
        
            color: <?php echo e($sub->Reg_Login_Form_Box_Txt_Color); ?> !important; 
         
     }
     
     .register-form form button{
         
           background: <?php echo e($sub->Reg_Login_Form_Box_Button_BG_Color); ?> !important;
    color: <?php echo e($sub->Reg_Login_Form_Box_Button_Txt_Color); ?> !important; 
     }
     
     
          .register-form form button:hover{
         
           background: <?php echo e($sub->Reg_Login_Form_Box_Button_BG_Hover_Color); ?> !important;
    color: <?php echo e($sub->Reg_Login_Form_Box_Button_Txt_Hover_Color); ?> !important; 
     } 
</style>
        <title><?php echo e(trans('admin.Register')); ?></title>
   
        <!-- Start Register Area -->
        <section class="register-area ptb-50">
            <div class="container">
                <div class="register-form">
                    <h2><?php echo e(trans('admin.Register')); ?></h2>

                    <form method="post" action="<?php echo e(url('PostRegister')); ?>">
                        <?php echo csrf_field(); ?>
                        <div class="form-group">
                            <input type="text" class="form-control" name="Name" placeholder="<?php echo e(trans('admin.Name')); ?>" value="<?php echo e(old('Name')); ?>" required>
                        </div>

                        <div class="form-group">
                            <input type="email" class="form-control"  name="email" placeholder="<?php echo e(trans('admin.Email')); ?>" value="<?php echo e(old('Email')); ?>" required>
                        </div>

                        <div class="form-group">
                            <input type="tel" class="form-control"  name="Phone" value="<?php echo e(old('Phone')); ?>" placeholder="<?php echo e(trans('admin.Phone')); ?>">
                        </div>

                        <div class="form-group">
                            <input type="password" class="form-control" type="password" value="<?php echo e(old('Password')); ?>" name="password" placeholder="<?php echo e(trans('admin.Password')); ?>">
                        </div>

                                 <div class="form-group">
                               
                              <select class="form-control" name="country"  required>
                                  <option value=""><?php echo e(trans('admin.Country')); ?></option>
                                  <?php $__currentLoopData = $Countris; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $count): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                               <option value="<?php echo e($count->id); ?>">
                     <?php echo e(app()->getLocale() == 'ar' ?$count->Arabic_Name :$count->English_Name); ?>

                               </option>      
                                  <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                              </select>                       	
                                </div>
 
                        <button type="submit"><?php echo e(trans('admin.Register')); ?></button>
                    </form>

                    <div class="important-text">
                        <p><?php echo e(trans('admin.Already_have_an_account')); ?> <a href="<?php echo e(url('LoginSite')); ?>"><?php echo e(trans('admin.Login')); ?></a></p>
                    </div>
                </div>
            </div>
        </section>
        <!-- End Register Area -->
<style>
    .register-form form .form-group .form-control {
        
      width: 100%;  
      padding: 19px;  
    }
</style>

<?php $__env->stopSection(); ?> 

<?php echo $__env->make('site.index', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\ost-erp-online\resources\views/site/Register.blade.php ENDPATH**/ ?>